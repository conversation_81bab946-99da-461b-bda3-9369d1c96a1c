import Foundation
import CoreData
import Combine

class SongListRepository {
    private let context: NSManagedObjectContext
    private let backgroundContext: NSManagedObjectContext
    
    init(context: NSManagedObjectContext) {
        self.context = context
        self.backgroundContext = NSManagedObjectContext(concurrencyType: .privateQueueConcurrencyType)
        self.backgroundContext.parent = context
    }
    
    // MARK: - CRUD Operations
    func getAllSongLists() -> AnyPublisher<[SongList], Error> {
        return Future { [weak self] promise in
            guard let self = self else {
                promise(.failure(RepositoryError.contextNotAvailable))
                return
            }
            
            self.backgroundContext.perform {
                let request: NSFetchRequest<SongList> = SongList.fetchRequest()
                request.sortDescriptors = [
                    NSSortDescriptor(keyPath: \SongList.sortOrder, ascending: true),
                    NSSortDescriptor(keyPath: \SongList.createdAt, ascending: false)
                ]
                
                do {
                    let songLists = try self.backgroundContext.fetch(request)
                    promise(.success(songLists))
                } catch {
                    promise(.failure(error))
                }
            }
        }
        .eraseToAnyPublisher()
    }
    
    func getCustomSongLists() -> AnyPublisher<[SongList], Error> {
        return Future { [weak self] promise in
            guard let self = self else {
                promise(.failure(RepositoryError.contextNotAvailable))
                return
            }
            
            self.backgroundContext.perform {
                let request: NSFetchRequest<SongList> = SongList.fetchRequest()
                request.predicate = NSPredicate(format: "type == %@", SongListType.custom.rawValue)
                request.sortDescriptors = [
                    NSSortDescriptor(keyPath: \SongList.sortOrder, ascending: true),
                    NSSortDescriptor(keyPath: \SongList.createdAt, ascending: false)
                ]
                
                do {
                    let songLists = try self.backgroundContext.fetch(request)
                    promise(.success(songLists))
                } catch {
                    promise(.failure(error))
                }
            }
        }
        .eraseToAnyPublisher()
    }
    
    func getSystemSongLists() -> AnyPublisher<[SongList], Error> {
        return Future { [weak self] promise in
            guard let self = self else {
                promise(.failure(RepositoryError.contextNotAvailable))
                return
            }
            
            self.backgroundContext.perform {
                let request: NSFetchRequest<SongList> = SongList.fetchRequest()
                request.predicate = NSPredicate(format: "type == %@", SongListType.system.rawValue)
                request.sortDescriptors = [
                    NSSortDescriptor(keyPath: \SongList.sortOrder, ascending: true)
                ]
                
                do {
                    let songLists = try self.backgroundContext.fetch(request)
                    promise(.success(songLists))
                } catch {
                    promise(.failure(error))
                }
            }
        }
        .eraseToAnyPublisher()
    }
    
    func createSongList(name: String, type: SongListType = .custom, systemType: SystemSongListType? = nil) -> AnyPublisher<SongList, Error> {
        return Future { [weak self] promise in
            guard let self = self else {
                promise(.failure(RepositoryError.contextNotAvailable))
                return
            }
            
            self.backgroundContext.perform {
                do {
                    let songList = SongList(context: self.backgroundContext,
                                          name: name,
                                          type: type,
                                          systemType: systemType)
                    
                    // 设置排序顺序
                    if type == .custom {
                        let maxSortOrder = self.getMaxSortOrder(for: .custom)
                        songList.sortOrder = maxSortOrder + 1
                    }
                    
                    try self.backgroundContext.save()
                    
                    DispatchQueue.main.async {
                        try? self.context.save()
                        promise(.success(songList))
                    }
                } catch {
                    promise(.failure(error))
                }
            }
        }
        .eraseToAnyPublisher()
    }
    
    func updateSongList(_ songList: SongList) -> AnyPublisher<Void, Error> {
        return Future { [weak self] promise in
            guard let self = self else {
                promise(.failure(RepositoryError.contextNotAvailable))
                return
            }
            
            self.backgroundContext.perform {
                do {
                    songList.updatedAt = Date()
                    try self.backgroundContext.save()
                    
                    DispatchQueue.main.async {
                        try? self.context.save()
                        promise(.success(()))
                    }
                } catch {
                    promise(.failure(error))
                }
            }
        }
        .eraseToAnyPublisher()
    }
    
    func deleteSongList(_ songList: SongList) -> AnyPublisher<Void, Error> {
        return Future { [weak self] promise in
            guard let self = self else {
                promise(.failure(RepositoryError.contextNotAvailable))
                return
            }
            
            // 只能删除自定义歌单
            guard songList.songListType == .custom else {
                promise(.failure(SongListRepositoryError.cannotDeleteSystemSongList))
                return
            }
            
            self.backgroundContext.perform {
                do {
                    self.backgroundContext.delete(songList)
                    try self.backgroundContext.save()
                    
                    DispatchQueue.main.async {
                        try? self.context.save()
                        promise(.success(()))
                    }
                } catch {
                    promise(.failure(error))
                }
            }
        }
        .eraseToAnyPublisher()
    }
    
    // MARK: - Song Management
    func addSongToSongList(_ song: Song, songList: SongList) -> AnyPublisher<Void, Error> {
        return Future { [weak self] promise in
            guard let self = self else {
                promise(.failure(RepositoryError.contextNotAvailable))
                return
            }
            
            // 只能向自定义歌单添加歌曲
            guard songList.songListType == .custom else {
                promise(.failure(SongListRepositoryError.cannotModifySystemSongList))
                return
            }
            
            self.backgroundContext.perform {
                do {
                    // 检查歌曲是否已存在
                    if songList.containsSong(song) {
                        promise(.failure(SongListRepositoryError.songAlreadyExists))
                        return
                    }
                    
                    try songList.addSong(song)
                    try self.backgroundContext.save()
                    
                    DispatchQueue.main.async {
                        try? self.context.save()
                        promise(.success(()))
                    }
                } catch {
                    promise(.failure(error))
                }
            }
        }
        .eraseToAnyPublisher()
    }
    
    func removeSongFromSongList(_ song: Song, songList: SongList) -> AnyPublisher<Void, Error> {
        return Future { [weak self] promise in
            guard let self = self else {
                promise(.failure(RepositoryError.contextNotAvailable))
                return
            }
            
            // 只能从自定义歌单移除歌曲
            guard songList.songListType == .custom else {
                promise(.failure(SongListRepositoryError.cannotModifySystemSongList))
                return
            }
            
            self.backgroundContext.perform {
                do {
                    try songList.removeSong(song)
                    try self.backgroundContext.save()
                    
                    DispatchQueue.main.async {
                        try? self.context.save()
                        promise(.success(()))
                    }
                } catch {
                    promise(.failure(error))
                }
            }
        }
        .eraseToAnyPublisher()
    }
    
    // MARK: - System Song List Management
    func initializeSystemSongLists() -> AnyPublisher<Void, Error> {
        return Future { [weak self] promise in
            guard let self = self else {
                promise(.failure(RepositoryError.contextNotAvailable))
                return
            }
            
            self.backgroundContext.perform {
                do {
                    // 检查系统歌单是否已存在
                    let request: NSFetchRequest<SongList> = SongList.fetchRequest()
                    request.predicate = NSPredicate(format: "type == %@", SongListType.system.rawValue)
                    let existingSongLists = try self.backgroundContext.fetch(request)
                    
                    let existingTypes = Set(existingSongLists.compactMap { $0.systemSongListType })
                    
                    // 创建缺失的系统歌单
                    for (index, systemType) in SystemSongListType.allCases.enumerated() {
                        if !existingTypes.contains(systemType) {
                            let songList = SongList(context: self.backgroundContext,
                                                  name: systemType.displayName,
                                                  type: .system,
                                                  systemType: systemType)
                            songList.sortOrder = Int32(index)
                        }
                    }
                    
                    try self.backgroundContext.save()
                    
                    DispatchQueue.main.async {
                        try? self.context.save()
                        promise(.success(()))
                    }
                } catch {
                    promise(.failure(error))
                }
            }
        }
        .eraseToAnyPublisher()
    }
    
    func updateSystemSongListCounts() -> AnyPublisher<Void, Error> {
        return Future { [weak self] promise in
            guard let self = self else {
                promise(.failure(RepositoryError.contextNotAvailable))
                return
            }
            
            self.backgroundContext.perform {
                do {
                    let songRepository = SongRepository(context: self.backgroundContext)
                    
                    // 获取所有系统歌单
                    let request: NSFetchRequest<SongList> = SongList.fetchRequest()
                    request.predicate = NSPredicate(format: "type == %@", SongListType.system.rawValue)
                    let systemSongLists = try self.backgroundContext.fetch(request)
                    
                    // 更新每个系统歌单的歌曲数量
                    for songList in systemSongLists {
                        guard let systemType = songList.systemSongListType else { continue }
                        
                        let count: Int
                        switch systemType {
                        case .all:
                            count = try self.getSongCount()
                        case .favorites:
                            count = try self.getFavoriteSongCount()
                        case .recent:
                            count = try self.getRecentSongCount()
                        case .local:
                            count = try self.getLocalSongCount()
                        case .unplayed:
                            count = try self.getUnplayedSongCount()
                        }
                        
                        songList.songCount = Int32(count)
                        songList.updatedAt = Date()
                    }
                    
                    try self.backgroundContext.save()
                    
                    DispatchQueue.main.async {
                        try? self.context.save()
                        promise(.success(()))
                    }
                } catch {
                    promise(.failure(error))
                }
            }
        }
        .eraseToAnyPublisher()
    }
    
    // MARK: - Private Helper Methods
    private func getMaxSortOrder(for type: SongListType) -> Int32 {
        let request: NSFetchRequest<SongList> = SongList.fetchRequest()
        request.predicate = NSPredicate(format: "type == %@", type.rawValue)
        request.sortDescriptors = [NSSortDescriptor(keyPath: \SongList.sortOrder, ascending: false)]
        request.fetchLimit = 1
        
        do {
            let songLists = try backgroundContext.fetch(request)
            return songLists.first?.sortOrder ?? 0
        } catch {
            return 0
        }
    }
    
    private func getSongCount() throws -> Int {
        let request: NSFetchRequest<Song> = Song.fetchRequest()
        return try backgroundContext.count(for: request)
    }
    
    private func getFavoriteSongCount() throws -> Int {
        let request: NSFetchRequest<Song> = Song.fetchRequest()
        request.predicate = NSPredicate(format: "isFavorite == YES")
        return try backgroundContext.count(for: request)
    }
    
    private func getRecentSongCount() throws -> Int {
        let request: NSFetchRequest<Song> = Song.fetchRequest()
        request.predicate = NSPredicate(format: "lastPlayedAt != nil")
        return try backgroundContext.count(for: request)
    }
    
    private func getLocalSongCount() throws -> Int {
        let request: NSFetchRequest<Song> = Song.fetchRequest()
        request.predicate = NSPredicate(format: "isLocal == YES")
        return try backgroundContext.count(for: request)
    }
    
    private func getUnplayedSongCount() throws -> Int {
        let request: NSFetchRequest<Song> = Song.fetchRequest()
        request.predicate = NSPredicate(format: "playCount == 0")
        return try backgroundContext.count(for: request)
    }

    // MARK: - Search Operations
    func searchSongLists(query: String) -> AnyPublisher<[SongList], Error> {
        return Future { [weak self] promise in
            guard let self = self else {
                promise(.failure(RepositoryError.contextNotAvailable))
                return
            }

            self.backgroundContext.perform {
                let request: NSFetchRequest<SongList> = SongList.fetchRequest()
                request.predicate = NSPredicate(format: "name CONTAINS[cd] %@", query)
                request.sortDescriptors = [NSSortDescriptor(keyPath: \SongList.name, ascending: true)]
                request.fetchLimit = 20

                do {
                    let songLists = try self.backgroundContext.fetch(request)
                    promise(.success(songLists))
                } catch {
                    promise(.failure(error))
                }
            }
        }
        .eraseToAnyPublisher()
    }
}

// MARK: - Repository Errors
enum SongListRepositoryError: LocalizedError {
    case cannotDeleteSystemSongList
    case cannotModifySystemSongList
    case songAlreadyExists
    case songNotFound
    
    var errorDescription: String? {
        switch self {
        case .cannotDeleteSystemSongList:
            return "无法删除系统歌单"
        case .cannotModifySystemSongList:
            return "无法修改系统歌单"
        case .songAlreadyExists:
            return "歌曲已存在于歌单中"
        case .songNotFound:
            return "歌单中未找到该歌曲"
        }
    }
}
