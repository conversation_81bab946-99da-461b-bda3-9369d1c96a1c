import Foundation
import CoreData
import Combine

protocol SongRepositoryProtocol {
    func getAllSongs() -> AnyPublisher<[Song], Error>
    func getSong(by id: String) -> AnyPublisher<Song?, Error>
    func getSongs(by filePaths: [String]) -> AnyPublisher<[Song], Error>
    func searchSongs(query: String) -> AnyPublisher<[Song], Error>
    func addSong(_ song: Song) -> AnyPublisher<Void, Error>
    func updateSong(_ song: Song) -> AnyPublisher<Void, Error>
    func deleteSong(_ song: Song) -> AnyPublisher<Void, Error>
    func markAsPlayed(_ song: Song, duration: Double) -> AnyPublisher<Void, Error>
    func toggleFavorite(_ song: Song) -> AnyPublisher<Void, Error>
    func getFavoriteSongs() -> AnyPublisher<[Song], Error>
    func getRecentSongs(limit: Int) -> AnyPublisher<[Song], Error>
    func getLocalSongs() -> AnyPublisher<[Song], Error>
    func getUnplayedSongs() -> AnyPublisher<[Song], Error>
}

class SongRepository: SongRepositoryProtocol {
    private let context: NSManagedObjectContext
    private let backgroundContext: NSManagedObjectContext
    
    init(context: NSManagedObjectContext) {
        self.context = context
        self.backgroundContext = NSManagedObjectContext(concurrencyType: .privateQueueConcurrencyType)
        self.backgroundContext.parent = context
    }
    
    // MARK: - Basic CRUD Operations
    func getAllSongs() -> AnyPublisher<[Song], Error> {
        return Future { [weak self] promise in
            guard let self = self else {
                promise(.failure(RepositoryError.contextNotAvailable))
                return
            }
            
            self.backgroundContext.perform {
                let request: NSFetchRequest<Song> = Song.fetchRequest()
                request.sortDescriptors = [NSSortDescriptor(keyPath: \Song.title, ascending: true)]
                
                do {
                    let songs = try self.backgroundContext.fetch(request)
                    promise(.success(songs))
                } catch {
                    promise(.failure(error))
                }
            }
        }
        .eraseToAnyPublisher()
    }
    
    func getSong(by id: String) -> AnyPublisher<Song?, Error> {
        return Future { [weak self] promise in
            guard let self = self else {
                promise(.failure(RepositoryError.contextNotAvailable))
                return
            }
            
            self.backgroundContext.perform {
                let request: NSFetchRequest<Song> = Song.fetchRequest()
                request.predicate = NSPredicate(format: "id == %@", id)
                request.fetchLimit = 1
                
                do {
                    let songs = try self.backgroundContext.fetch(request)
                    promise(.success(songs.first))
                } catch {
                    promise(.failure(error))
                }
            }
        }
        .eraseToAnyPublisher()
    }
    
    func getSongs(by filePaths: [String]) -> AnyPublisher<[Song], Error> {
        return Future { [weak self] promise in
            guard let self = self else {
                promise(.failure(RepositoryError.contextNotAvailable))
                return
            }
            
            self.backgroundContext.perform {
                let request: NSFetchRequest<Song> = Song.fetchRequest()
                request.predicate = NSPredicate(format: "filePath IN %@", filePaths)
                request.sortDescriptors = [NSSortDescriptor(keyPath: \Song.title, ascending: true)]
                
                do {
                    let songs = try self.backgroundContext.fetch(request)
                    promise(.success(songs))
                } catch {
                    promise(.failure(error))
                }
            }
        }
        .eraseToAnyPublisher()
    }
    
    func addSong(_ song: Song) -> AnyPublisher<Void, Error> {
        return Future { [weak self] promise in
            guard let self = self else {
                promise(.failure(RepositoryError.contextNotAvailable))
                return
            }
            
            self.backgroundContext.perform {
                do {
                    try song.validate()
                    try self.backgroundContext.save()
                    
                    DispatchQueue.main.async {
                        try? self.context.save()
                        promise(.success(()))
                    }
                } catch {
                    promise(.failure(error))
                }
            }
        }
        .eraseToAnyPublisher()
    }
    
    func updateSong(_ song: Song) -> AnyPublisher<Void, Error> {
        return Future { [weak self] promise in
            guard let self = self else {
                promise(.failure(RepositoryError.contextNotAvailable))
                return
            }
            
            self.backgroundContext.perform {
                do {
                    song.updatedAt = Date()
                    try song.validate()
                    try self.backgroundContext.save()
                    
                    DispatchQueue.main.async {
                        try? self.context.save()
                        promise(.success(()))
                    }
                } catch {
                    promise(.failure(error))
                }
            }
        }
        .eraseToAnyPublisher()
    }
    
    func deleteSong(_ song: Song) -> AnyPublisher<Void, Error> {
        return Future { [weak self] promise in
            guard let self = self else {
                promise(.failure(RepositoryError.contextNotAvailable))
                return
            }
            
            self.backgroundContext.perform {
                do {
                    self.backgroundContext.delete(song)
                    try self.backgroundContext.save()
                    
                    DispatchQueue.main.async {
                        try? self.context.save()
                        promise(.success(()))
                    }
                } catch {
                    promise(.failure(error))
                }
            }
        }
        .eraseToAnyPublisher()
    }
    
    // MARK: - Search Operations
    func searchSongs(query: String) -> AnyPublisher<[Song], Error> {
        return Future { [weak self] promise in
            guard let self = self else {
                promise(.failure(RepositoryError.contextNotAvailable))
                return
            }
            
            self.backgroundContext.perform {
                let request: NSFetchRequest<Song> = Song.fetchRequest()
                
                // 构建搜索谓词
                let searchTerms = query.trimmingCharacters(in: .whitespacesAndNewlines)
                    .lowercased()
                    .components(separatedBy: " ")
                    .filter { !$0.isEmpty }
                
                var predicates: [NSPredicate] = []
                
                for term in searchTerms {
                    let termPredicate = NSPredicate(format: 
                        "title CONTAINS[cd] %@ OR artist CONTAINS[cd] %@ OR album CONTAINS[cd] %@ OR filePath CONTAINS[cd] %@",
                        term, term, term, term)
                    predicates.append(termPredicate)
                }
                
                if !predicates.isEmpty {
                    request.predicate = NSCompoundPredicate(andPredicateWithSubpredicates: predicates)
                }
                
                request.sortDescriptors = [
                    NSSortDescriptor(keyPath: \Song.title, ascending: true)
                ]
                request.fetchLimit = 100
                
                do {
                    let songs = try self.backgroundContext.fetch(request)
                    promise(.success(songs))
                } catch {
                    promise(.failure(error))
                }
            }
        }
        .eraseToAnyPublisher()
    }
    
    // MARK: - Play Statistics
    func markAsPlayed(_ song: Song, duration: Double = 0.0) -> AnyPublisher<Void, Error> {
        return Future { [weak self] promise in
            guard let self = self else {
                promise(.failure(RepositoryError.contextNotAvailable))
                return
            }
            
            self.backgroundContext.perform {
                do {
                    song.markAsPlayed(duration: duration)
                    
                    // 创建播放历史记录
                    let playHistory = PlayHistory(context: self.backgroundContext,
                                                song: song,
                                                playDuration: duration)
                    
                    try self.backgroundContext.save()
                    
                    DispatchQueue.main.async {
                        try? self.context.save()
                        promise(.success(()))
                    }
                } catch {
                    promise(.failure(error))
                }
            }
        }
        .eraseToAnyPublisher()
    }
    
    func toggleFavorite(_ song: Song) -> AnyPublisher<Void, Error> {
        return Future { [weak self] promise in
            guard let self = self else {
                promise(.failure(RepositoryError.contextNotAvailable))
                return
            }
            
            self.backgroundContext.perform {
                do {
                    song.toggleFavorite()
                    try self.backgroundContext.save()
                    
                    DispatchQueue.main.async {
                        try? self.context.save()
                        promise(.success(()))
                    }
                } catch {
                    promise(.failure(error))
                }
            }
        }
        .eraseToAnyPublisher()
    }
    
    // MARK: - System Playlists
    func getFavoriteSongs() -> AnyPublisher<[Song], Error> {
        return Future { [weak self] promise in
            guard let self = self else {
                promise(.failure(RepositoryError.contextNotAvailable))
                return
            }
            
            self.backgroundContext.perform {
                let request: NSFetchRequest<Song> = Song.fetchRequest()
                request.predicate = NSPredicate(format: "isFavorite == YES")
                request.sortDescriptors = [NSSortDescriptor(keyPath: \Song.updatedAt, ascending: false)]
                
                do {
                    let songs = try self.backgroundContext.fetch(request)
                    promise(.success(songs))
                } catch {
                    promise(.failure(error))
                }
            }
        }
        .eraseToAnyPublisher()
    }
    
    func getRecentSongs(limit: Int = 200) -> AnyPublisher<[Song], Error> {
        return Future { [weak self] promise in
            guard let self = self else {
                promise(.failure(RepositoryError.contextNotAvailable))
                return
            }
            
            self.backgroundContext.perform {
                let request: NSFetchRequest<Song> = Song.fetchRequest()
                request.predicate = NSPredicate(format: "lastPlayedAt != nil")
                request.sortDescriptors = [NSSortDescriptor(keyPath: \Song.lastPlayedAt, ascending: false)]
                request.fetchLimit = limit
                
                do {
                    let songs = try self.backgroundContext.fetch(request)
                    promise(.success(songs))
                } catch {
                    promise(.failure(error))
                }
            }
        }
        .eraseToAnyPublisher()
    }
    
    func getLocalSongs() -> AnyPublisher<[Song], Error> {
        return Future { [weak self] promise in
            guard let self = self else {
                promise(.failure(RepositoryError.contextNotAvailable))
                return
            }
            
            self.backgroundContext.perform {
                let request: NSFetchRequest<Song> = Song.fetchRequest()
                request.predicate = NSPredicate(format: "isLocal == YES")
                request.sortDescriptors = [NSSortDescriptor(keyPath: \Song.title, ascending: true)]
                
                do {
                    let songs = try self.backgroundContext.fetch(request)
                    promise(.success(songs))
                } catch {
                    promise(.failure(error))
                }
            }
        }
        .eraseToAnyPublisher()
    }
    
    func getUnplayedSongs() -> AnyPublisher<[Song], Error> {
        return Future { [weak self] promise in
            guard let self = self else {
                promise(.failure(RepositoryError.contextNotAvailable))
                return
            }
            
            self.backgroundContext.perform {
                let request: NSFetchRequest<Song> = Song.fetchRequest()
                request.predicate = NSPredicate(format: "playCount == 0")
                request.sortDescriptors = [NSSortDescriptor(keyPath: \Song.createdAt, ascending: false)]
                
                do {
                    let songs = try self.backgroundContext.fetch(request)
                    promise(.success(songs))
                } catch {
                    promise(.failure(error))
                }
            }
        }
        .eraseToAnyPublisher()
    }

    // MARK: - Search Operations
    func searchSongs(query: String) -> AnyPublisher<[Song], Error> {
        return Future { [weak self] promise in
            guard let self = self else {
                promise(.failure(RepositoryError.contextNotAvailable))
                return
            }

            self.backgroundContext.perform {
                let request: NSFetchRequest<Song> = Song.fetchRequest()
                request.predicate = NSPredicate(format: "title CONTAINS[cd] %@ OR artist CONTAINS[cd] %@ OR album CONTAINS[cd] %@", query, query, query)
                request.sortDescriptors = [NSSortDescriptor(keyPath: \Song.title, ascending: true)]
                request.fetchLimit = 50

                do {
                    let songs = try self.backgroundContext.fetch(request)
                    promise(.success(songs))
                } catch {
                    promise(.failure(error))
                }
            }
        }
        .eraseToAnyPublisher()
    }

    func searchArtists(query: String) -> AnyPublisher<[String], Error> {
        return Future { [weak self] promise in
            guard let self = self else {
                promise(.failure(RepositoryError.contextNotAvailable))
                return
            }

            self.backgroundContext.perform {
                let request: NSFetchRequest<Song> = Song.fetchRequest()
                request.predicate = NSPredicate(format: "artist CONTAINS[cd] %@", query)
                request.propertiesToFetch = ["artist"]
                request.returnsDistinctResults = true
                request.resultType = .dictionaryResultType

                do {
                    let results = try self.backgroundContext.fetch(request) as! [[String: Any]]
                    let artists = results.compactMap { $0["artist"] as? String }
                        .filter { !$0.isEmpty }
                        .sorted()
                    promise(.success(Array(Set(artists))))
                } catch {
                    promise(.failure(error))
                }
            }
        }
        .eraseToAnyPublisher()
    }

    func searchAlbums(query: String) -> AnyPublisher<[String], Error> {
        return Future { [weak self] promise in
            guard let self = self else {
                promise(.failure(RepositoryError.contextNotAvailable))
                return
            }

            self.backgroundContext.perform {
                let request: NSFetchRequest<Song> = Song.fetchRequest()
                request.predicate = NSPredicate(format: "album CONTAINS[cd] %@", query)
                request.propertiesToFetch = ["album"]
                request.returnsDistinctResults = true
                request.resultType = .dictionaryResultType

                do {
                    let results = try self.backgroundContext.fetch(request) as! [[String: Any]]
                    let albums = results.compactMap { $0["album"] as? String }
                        .filter { !$0.isEmpty }
                        .sorted()
                    promise(.success(Array(Set(albums))))
                } catch {
                    promise(.failure(error))
                }
            }
        }
        .eraseToAnyPublisher()
    }
}

// MARK: - Repository Errors
enum RepositoryError: LocalizedError {
    case contextNotAvailable
    case invalidData
    case saveFailed
    
    var errorDescription: String? {
        switch self {
        case .contextNotAvailable:
            return "数据上下文不可用"
        case .invalidData:
            return "无效的数据"
        case .saveFailed:
            return "保存失败"
        }
    }
}
