import Foundation
import Combine

class CloudStorageManager: ObservableObject {
    static let shared = CloudStorageManager()
    
    // MARK: - Published Properties
    @Published var isConnected: Bool = false
    @Published var isLoading: Bool = false
    @Published var errorMessage: String?
    
    // MARK: - Configuration
    private var ossConfig: OSSConfig?
    private var ossClient: OSSClient?
    
    // MARK: - Dependencies
    private var cacheManager: CacheManager?
    
    private init() {
        loadConfiguration()
    }
    
    // MARK: - Dependency Injection
    func setCacheManager(_ cacheManager: CacheManager) {
        self.cacheManager = cacheManager
    }
    
    // MARK: - Configuration
    func configure(with config: OSSConfig) {
        self.ossConfig = config
        self.ossClient = OSSClient(config: config)
        
        Task {
            await testConnection()
        }
    }
    
    private func loadConfiguration() {
        // 从UserDefaults加载配置
        if let configData = UserDefaults.standard.data(forKey: "OSSConfig"),
           let config = try? JSONDecoder().decode(OSSConfig.self, from: configData) {
            configure(with: config)
        }
    }
    
    private func saveConfiguration() {
        guard let config = ossConfig else { return }
        
        if let configData = try? JSONEncoder().encode(config) {
            UserDefaults.standard.set(configData, forKey: "OSSConfig")
        }
    }
    
    @MainActor
    private func testConnection() async {
        guard let ossClient = ossClient else {
            isConnected = false
            return
        }
        
        isLoading = true
        
        do {
            _ = try await ossClient.listBuckets()
            isConnected = true
            errorMessage = nil
        } catch {
            isConnected = false
            errorMessage = "连接失败: \(error.localizedDescription)"
        }
        
        isLoading = false
    }
    
    // MARK: - Folder Operations
    func listFolders() async throws -> [CloudFolder] {
        guard let ossClient = ossClient else {
            throw CloudStorageError.notConfigured
        }
        
        let objects = try await ossClient.listObjects(prefix: "", delimiter: "/")
        
        return objects.compactMap { object in
            guard object.key.hasSuffix("/") else { return nil }
            
            let folderName = String(object.key.dropLast())
            return CloudFolder(
                name: folderName,
                path: object.key,
                fileCount: 0, // 需要单独查询
                modifiedDate: object.lastModified
            )
        }
    }
    
    func listFiles(in folderPath: String) async throws -> [CloudFile] {
        guard let ossClient = ossClient else {
            throw CloudStorageError.notConfigured
        }
        
        let objects = try await ossClient.listObjects(prefix: folderPath, delimiter: "/")
        
        return objects.compactMap { object in
            guard !object.key.hasSuffix("/") else { return nil }
            
            let fileName = String(object.key.split(separator: "/").last ?? "")
            let isDownloaded = cacheManager?.isFileCached(object.key) ?? false
            
            return CloudFile(
                name: fileName,
                path: object.key,
                size: object.size,
                modifiedDate: object.lastModified,
                isDownloaded: isDownloaded
            )
        }
    }
    
    // MARK: - File Operations
    func downloadFile(_ file: CloudFile) async throws {
        guard let ossClient = ossClient,
              let cacheManager = cacheManager else {
            throw CloudStorageError.notConfigured
        }
        
        // 检查是否已缓存
        if cacheManager.isFileCached(file.path) {
            return
        }
        
        let fileData = try await ossClient.getObject(key: file.path)
        try cacheManager.cacheFile(data: fileData, key: file.path)
    }
    
    func uploadFile(data: Data, to path: String) async throws {
        guard let ossClient = ossClient else {
            throw CloudStorageError.notConfigured
        }
        
        try await ossClient.putObject(key: path, data: data)
    }
    
    func deleteFile(at path: String) async throws {
        guard let ossClient = ossClient else {
            throw CloudStorageError.notConfigured
        }
        
        try await ossClient.deleteObject(key: path)
        
        // 同时删除本地缓存
        cacheManager?.removeCachedFile(key: path)
    }
    
    // MARK: - Sync Operations
    func syncWithCloud() async throws {
        // 实现云端同步逻辑
        // 1. 获取云端文件列表
        // 2. 比较本地数据库
        // 3. 下载新文件的元数据
        // 4. 更新本地数据库
    }
}

// MARK: - OSS Configuration
struct OSSConfig: Codable {
    let endpoint: String
    let accessKeyId: String
    let accessKeySecret: String
    let bucketName: String
    let securityToken: String?
    
    init(endpoint: String, accessKeyId: String, accessKeySecret: String, bucketName: String, securityToken: String? = nil) {
        self.endpoint = endpoint
        self.accessKeyId = accessKeyId
        self.accessKeySecret = accessKeySecret
        self.bucketName = bucketName
        self.securityToken = securityToken
    }
}

// MARK: - OSS Client (Mock Implementation)
class OSSClient {
    private let config: OSSConfig
    
    init(config: OSSConfig) {
        self.config = config
    }
    
    func listBuckets() async throws -> [String] {
        // Mock implementation
        // 实际实现需要使用阿里云OSS SDK
        try await Task.sleep(nanoseconds: 1_000_000_000) // 1秒延迟模拟网络请求
        return [config.bucketName]
    }
    
    func listObjects(prefix: String, delimiter: String) async throws -> [OSSObject] {
        // Mock implementation
        // 实际实现需要使用阿里云OSS SDK
        try await Task.sleep(nanoseconds: 500_000_000) // 0.5秒延迟
        
        // 返回模拟数据
        return [
            OSSObject(key: "music/", size: 0, lastModified: Date()),
            OSSObject(key: "music/song1.mp3", size: 5_000_000, lastModified: Date()),
            OSSObject(key: "music/song2.mp3", size: 4_500_000, lastModified: Date()),
        ]
    }
    
    func getObject(key: String) async throws -> Data {
        // Mock implementation
        try await Task.sleep(nanoseconds: 2_000_000_000) // 2秒延迟模拟下载
        return Data() // 返回空数据作为示例
    }
    
    func putObject(key: String, data: Data) async throws {
        // Mock implementation
        try await Task.sleep(nanoseconds: 1_000_000_000) // 1秒延迟模拟上传
    }
    
    func deleteObject(key: String) async throws {
        // Mock implementation
        try await Task.sleep(nanoseconds: 500_000_000) // 0.5秒延迟
    }
}

// MARK: - OSS Object
struct OSSObject {
    let key: String
    let size: Int64
    let lastModified: Date
}

// MARK: - Cloud Storage Errors
enum CloudStorageError: LocalizedError {
    case notConfigured
    case connectionFailed
    case fileNotFound
    case uploadFailed
    case downloadFailed
    
    var errorDescription: String? {
        switch self {
        case .notConfigured:
            return "云存储未配置"
        case .connectionFailed:
            return "连接失败"
        case .fileNotFound:
            return "文件未找到"
        case .uploadFailed:
            return "上传失败"
        case .downloadFailed:
            return "下载失败"
        }
    }
}
