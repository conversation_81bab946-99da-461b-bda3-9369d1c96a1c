import Foundation
import Combine
import CoreData

class CacheManager: ObservableObject {
    static let shared = CacheManager()
    
    // MARK: - Published Properties
    @Published var totalCacheSize: Int64 = 0
    @Published var availableSpace: Int64 = 0
    @Published var downloadProgress: [String: Float] = [:]
    
    // MARK: - Private Properties
    private let fileManager = FileManager.default
    private let cacheDirectory: URL
    private let maxCacheSize: Int64 = 2 * 1024 * 1024 * 1024 // 2GB
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Dependencies
    private var ossManager: OSSManager?
    private var context: NSManagedObjectContext?
    
    init() {
        // 创建缓存目录
        let documentsPath = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first!
        cacheDirectory = documentsPath.appendingPathComponent("MusicCache")
        
        createCacheDirectoryIfNeeded()
        updateCacheInfo()
    }
    
    // MARK: - Dependency Injection
    func setOSSManager(_ ossManager: OSSManager) {
        self.ossManager = ossManager
    }
    
    func setContext(_ context: NSManagedObjectContext) {
        self.context = context
    }
    
    // MARK: - Public Methods
    func getCacheUsage() -> Int64 {
        return totalCacheSize
    }

    func getCachedFilesCount() -> Int {
        do {
            let files = try fileManager.contentsOfDirectory(at: cacheDirectory, includingPropertiesForKeys: nil)
            return files.count
        } catch {
            return 0
        }
    }

    func getCachedFilesList() -> [CachedFileInfo] {
        do {
            let files = try fileManager.contentsOfDirectory(at: cacheDirectory, includingPropertiesForKeys: [.fileSizeKey, .contentAccessDateKey])

            return files.compactMap { url in
                guard let resources = try? url.resourceValues(forKeys: [.fileSizeKey, .contentAccessDateKey]),
                      let size = resources.fileSize,
                      let lastAccessed = resources.contentAccessDate else {
                    return nil
                }

                return CachedFileInfo(
                    name: url.lastPathComponent,
                    path: url.path,
                    size: Int64(size),
                    lastAccessed: lastAccessed
                )
            }.sorted { $0.lastAccessed > $1.lastAccessed }
        } catch {
            return []
        }
    }

    func isFileCached(_ key: String) -> Bool {
        let localURL = cacheDirectory.appendingPathComponent(key.replacingOccurrences(of: "/", with: "_"))
        return fileManager.fileExists(atPath: localURL.path)
    }

    func cacheFile(data: Data, key: String) throws {
        let localURL = cacheDirectory.appendingPathComponent(key.replacingOccurrences(of: "/", with: "_"))
        try data.write(to: localURL)
        updateCacheInfo()
    }

    func removeCachedFile(key: String) {
        let localURL = cacheDirectory.appendingPathComponent(key.replacingOccurrences(of: "/", with: "_"))
        try? fileManager.removeItem(at: localURL)
        updateCacheInfo()
    }

    func cleanupOldFiles() async throws {
        let files = getCachedFilesList()
        let cutoffDate = Calendar.current.date(byAdding: .day, value: -30, to: Date()) ?? Date()

        for file in files {
            if file.lastAccessed < cutoffDate {
                try? fileManager.removeItem(atPath: file.path)
            }
        }

        updateCacheInfo()
    }

    func clearAllCache() async throws {
        let files = try fileManager.contentsOfDirectory(at: cacheDirectory, includingPropertiesForKeys: nil)

        for file in files {
            try fileManager.removeItem(at: file)
        }

        updateCacheInfo()
    }

    func getCachedSongURL(for song: Song) -> AnyPublisher<URL?, Error> {
        return Future { [weak self] promise in
            guard let self = self else {
                promise(.failure(CacheError.managerNotAvailable))
                return
            }

            let localPath = self.localPath(for: song)
            
            if self.fileManager.fileExists(atPath: localPath.path) {
                // 更新最后访问时间
                self.updateLastAccessed(for: song)
                promise(.success(localPath))
            } else {
                promise(.success(nil))
            }
        }
        .eraseToAnyPublisher()
    }
    
    func downloadSong(_ song: Song) -> AnyPublisher<URL, Error> {
        return Future { [weak self] promise in
            guard let self = self,
                  let ossManager = self.ossManager else {
                promise(.failure(CacheError.dependencyNotAvailable))
                return
            }
            
            let localPath = self.localPath(for: song)
            
            // 检查是否已经存在
            if self.fileManager.fileExists(atPath: localPath.path) {
                self.updateLastAccessed(for: song)
                promise(.success(localPath))
                return
            }
            
            // 检查缓存空间
            self.ensureCacheSpace(for: song.fileSize)
                .flatMap { _ in
                    // 开始下载
                    ossManager.downloadFile(from: song.filePath, to: localPath)
                        .handleEvents(
                            receiveSubscription: { _ in
                                DispatchQueue.main.async {
                                    self.downloadProgress[song.id] = 0.0
                                }
                                self.createCacheInfo(for: song, localPath: localPath)
                            },
                            receiveOutput: { progress in
                                DispatchQueue.main.async {
                                    self.downloadProgress[song.id] = progress
                                }
                                self.updateDownloadProgress(for: song, progress: progress)
                            },
                            receiveCompletion: { completion in
                                DispatchQueue.main.async {
                                    self.downloadProgress.removeValue(forKey: song.id)
                                }
                                
                                if case .finished = completion {
                                    self.completeCacheInfo(for: song)
                                    song.markAsLocal(localPath: localPath.path)
                                    self.updateCacheInfo()
                                }
                            }
                        )
                        .map { _ in localPath }
                }
                .sink(
                    receiveCompletion: { completion in
                        if case .failure(let error) = completion {
                            promise(.failure(error))
                        }
                    },
                    receiveValue: { url in
                        promise(.success(url))
                    }
                )
                .store(in: &self.cancellables)
        }
        .eraseToAnyPublisher()
    }
    
    func removeCachedSong(_ song: Song) -> AnyPublisher<Void, Error> {
        return Future { [weak self] promise in
            guard let self = self else {
                promise(.failure(CacheError.managerNotAvailable))
                return
            }
            
            let localPath = self.localPath(for: song)
            
            do {
                if self.fileManager.fileExists(atPath: localPath.path) {
                    try self.fileManager.removeItem(at: localPath)
                }
                
                // 更新数据库
                song.markAsRemote()
                self.removeCacheInfo(for: song)
                self.updateCacheInfo()
                
                promise(.success(()))
            } catch {
                promise(.failure(error))
            }
        }
        .eraseToAnyPublisher()
    }
    
    func clearCache() -> AnyPublisher<Void, Error> {
        return Future { [weak self] promise in
            guard let self = self else {
                promise(.failure(CacheError.managerNotAvailable))
                return
            }
            
            do {
                // 删除缓存目录
                if self.fileManager.fileExists(atPath: self.cacheDirectory.path) {
                    try self.fileManager.removeItem(at: self.cacheDirectory)
                }
                
                // 重新创建目录
                self.createCacheDirectoryIfNeeded()
                
                // 清理数据库中的缓存信息
                self.clearAllCacheInfo()
                self.updateCacheInfo()
                
                promise(.success(()))
            } catch {
                promise(.failure(error))
            }
        }
        .eraseToAnyPublisher()
    }
    
    func cleanupOldCache() -> AnyPublisher<Void, Error> {
        return Future { [weak self] promise in
            guard let self = self,
                  let context = self.context else {
                promise(.failure(CacheError.dependencyNotAvailable))
                return
            }
            
            // 获取所有缓存信息，按最后访问时间排序
            let request: NSFetchRequest<CacheInfo> = CacheInfo.fetchRequest()
            request.sortDescriptors = [NSSortDescriptor(keyPath: \CacheInfo.lastAccessedAt, ascending: true)]
            
            do {
                let cacheInfos = try context.fetch(request)
                var currentCacheSize = self.totalCacheSize
                
                // 删除最旧的缓存直到大小合适
                for cacheInfo in cacheInfos {
                    if currentCacheSize <= self.maxCacheSize * 8 / 10 { // 保持在80%以下
                        break
                    }
                    
                    let localURL = URL(fileURLWithPath: cacheInfo.localPath)
                    if self.fileManager.fileExists(atPath: localURL.path) {
                        try self.fileManager.removeItem(at: localURL)
                        currentCacheSize -= cacheInfo.fileSize
                        
                        // 更新歌曲状态
                        cacheInfo.song?.markAsRemote()
                    }
                    
                    // 删除缓存信息
                    context.delete(cacheInfo)
                }
                
                try context.save()
                self.updateCacheInfo()
                promise(.success(()))
            } catch {
                promise(.failure(error))
            }
        }
        .eraseToAnyPublisher()
    }
    
    // MARK: - Private Methods
    private func createCacheDirectoryIfNeeded() {
        if !fileManager.fileExists(atPath: cacheDirectory.path) {
            try? fileManager.createDirectory(at: cacheDirectory, withIntermediateDirectories: true)
        }
    }
    
    private func localPath(for song: Song) -> URL {
        let fileName = "\(song.id).\(song.fileExtension)"
        return cacheDirectory.appendingPathComponent(fileName)
    }
    
    private func updateCacheInfo() {
        DispatchQueue.global(qos: .utility).async { [weak self] in
            guard let self = self else { return }
            
            let cacheSize = self.calculateCacheSize()
            let availableSpace = self.calculateAvailableSpace()
            
            DispatchQueue.main.async {
                self.totalCacheSize = cacheSize
                self.availableSpace = availableSpace
            }
        }
    }
    
    private func calculateCacheSize() -> Int64 {
        guard let enumerator = fileManager.enumerator(at: cacheDirectory, includingPropertiesForKeys: [.fileSizeKey]) else {
            return 0
        }
        
        var totalSize: Int64 = 0
        
        for case let fileURL as URL in enumerator {
            do {
                let resourceValues = try fileURL.resourceValues(forKeys: [.fileSizeKey])
                totalSize += Int64(resourceValues.fileSize ?? 0)
            } catch {
                continue
            }
        }
        
        return totalSize
    }
    
    private func calculateAvailableSpace() -> Int64 {
        do {
            let systemAttributes = try fileManager.attributesOfFileSystem(forPath: cacheDirectory.path)
            return systemAttributes[.systemFreeSize] as? Int64 ?? 0
        } catch {
            return 0
        }
    }
    
    private func ensureCacheSpace(for fileSize: Int64) -> AnyPublisher<Void, Error> {
        if totalCacheSize + fileSize > maxCacheSize {
            return cleanupOldCache()
        } else {
            return Just(())
                .setFailureType(to: Error.self)
                .eraseToAnyPublisher()
        }
    }
    
    // MARK: - Core Data Operations
    private func createCacheInfo(for song: Song, localPath: URL) {
        guard let context = context else { return }
        
        let cacheInfo = CacheInfo(context: context,
                                song: song,
                                filePath: song.filePath,
                                localPath: localPath.path,
                                fileSize: song.fileSize)
        cacheInfo.startDownload()
        
        try? context.save()
    }
    
    private func updateDownloadProgress(for song: Song, progress: Float) {
        guard let context = context else { return }
        
        let request: NSFetchRequest<CacheInfo> = CacheInfo.fetchRequest()
        request.predicate = NSPredicate(format: "song == %@", song)
        request.fetchLimit = 1
        
        if let cacheInfo = try? context.fetch(request).first {
            cacheInfo.updateDownloadProgress(progress)
            try? context.save()
        }
    }
    
    private func completeCacheInfo(for song: Song) {
        guard let context = context else { return }
        
        let request: NSFetchRequest<CacheInfo> = CacheInfo.fetchRequest()
        request.predicate = NSPredicate(format: "song == %@", song)
        request.fetchLimit = 1
        
        if let cacheInfo = try? context.fetch(request).first {
            cacheInfo.updateDownloadProgress(1.0)
            try? context.save()
        }
    }
    
    private func updateLastAccessed(for song: Song) {
        guard let context = context else { return }
        
        let request: NSFetchRequest<CacheInfo> = CacheInfo.fetchRequest()
        request.predicate = NSPredicate(format: "song == %@", song)
        request.fetchLimit = 1
        
        if let cacheInfo = try? context.fetch(request).first {
            cacheInfo.updateLastAccessed()
            try? context.save()
        }
    }
    
    private func removeCacheInfo(for song: Song) {
        guard let context = context else { return }
        
        let request: NSFetchRequest<CacheInfo> = CacheInfo.fetchRequest()
        request.predicate = NSPredicate(format: "song == %@", song)
        
        if let cacheInfos = try? context.fetch(request) {
            for cacheInfo in cacheInfos {
                context.delete(cacheInfo)
            }
            try? context.save()
        }
    }
    
    private func clearAllCacheInfo() {
        guard let context = context else { return }
        
        let request: NSFetchRequest<CacheInfo> = CacheInfo.fetchRequest()
        
        if let cacheInfos = try? context.fetch(request) {
            for cacheInfo in cacheInfos {
                cacheInfo.song?.markAsRemote()
                context.delete(cacheInfo)
            }
            try? context.save()
        }
    }
}

// MARK: - Cache Errors
enum CacheError: LocalizedError {
    case managerNotAvailable
    case dependencyNotAvailable
    case insufficientSpace
    case fileNotFound
    case downloadFailed
    
    var errorDescription: String? {
        switch self {
        case .managerNotAvailable:
            return "缓存管理器不可用"
        case .dependencyNotAvailable:
            return "依赖服务不可用"
        case .insufficientSpace:
            return "存储空间不足"
        case .fileNotFound:
            return "文件未找到"
        case .downloadFailed:
            return "下载失败"
        }
    }
}
