import Foundation
import AVFoundation
import Combine
import MediaPlayer

class AudioPlayerManager: NSObject, ObservableObject {
    static let shared = AudioPlayerManager()
    
    // MARK: - Published Properties
    @Published var isPlaying: Bool = false
    @Published var currentTime: TimeInterval = 0
    @Published var duration: TimeInterval = 0
    @Published var currentSong: Song? = nil
    @Published var playQueue: [Song] = []
    @Published var currentIndex: Int = 0
    @Published var playMode: PlayMode = .sequential
    @Published var volume: Float = 1.0
    @Published var isLoading: Bool = false
    
    // MARK: - Private Properties
    private var player: AVPlayer?
    private var timeObserver: Any?
    private var cancellables = Set<AnyCancellable>()
    private let audioSession = AVAudioSession.sharedInstance()
    
    // MARK: - Dependencies
    private var cacheManager: CacheManager?
    private var songRepository: SongRepository?
    
    override init() {
        super.init()
        setupAudioSession()
        setupRemoteTransportControls()
        setupNotifications()
    }
    
    deinit {
        if let timeObserver = timeObserver {
            player?.removeTimeObserver(timeObserver)
        }
        NotificationCenter.default.removeObserver(self)
    }
    
    // MARK: - Dependency Injection
    func setCacheManager(_ cacheManager: CacheManager) {
        self.cacheManager = cacheManager
    }
    
    func setSongRepository(_ songRepository: SongRepository) {
        self.songRepository = songRepository
    }
    
    // MARK: - Audio Session Setup
    private func setupAudioSession() {
        do {
            try audioSession.setCategory(.playback, mode: .default, options: [.allowAirPlay, .allowBluetooth])
            try audioSession.setActive(true)
        } catch {
            print("Failed to setup audio session: \(error)")
        }
    }
    
    // MARK: - Remote Control Setup
    private func setupRemoteTransportControls() {
        let commandCenter = MPRemoteCommandCenter.shared()
        
        commandCenter.playCommand.addTarget { [weak self] _ in
            self?.resume()
            return .success
        }
        
        commandCenter.pauseCommand.addTarget { [weak self] _ in
            self?.pause()
            return .success
        }
        
        commandCenter.nextTrackCommand.addTarget { [weak self] _ in
            self?.next()
            return .success
        }
        
        commandCenter.previousTrackCommand.addTarget { [weak self] _ in
            self?.previous()
            return .success
        }
        
        commandCenter.changePlaybackPositionCommand.addTarget { [weak self] event in
            guard let event = event as? MPChangePlaybackPositionCommandEvent else {
                return .commandFailed
            }
            self?.seek(to: event.positionTime)
            return .success
        }
    }
    
    // MARK: - Notifications Setup
    private func setupNotifications() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(playerDidFinishPlaying),
            name: .AVPlayerItemDidPlayToEndTime,
            object: nil
        )
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(playerItemFailedToPlay),
            name: .AVPlayerItemFailedToPlayToEndTime,
            object: nil
        )
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(audioSessionInterrupted),
            name: AVAudioSession.interruptionNotification,
            object: nil
        )
    }
    
    // MARK: - Playback Control
    func play(song: Song) {
        play(songs: [song], startIndex: 0)
    }
    
    func play(songs: [Song], startIndex: Int = 0) {
        guard !songs.isEmpty && startIndex < songs.count else { return }
        
        playQueue = songs
        currentIndex = startIndex
        currentSong = songs[startIndex]
        
        loadAndPlayCurrentSong()
    }
    
    func pause() {
        player?.pause()
        isPlaying = false
        updateNowPlayingInfo()
    }
    
    func resume() {
        player?.play()
        isPlaying = true
        updateNowPlayingInfo()
    }
    
    func stop() {
        player?.pause()
        player = nil
        isPlaying = false
        currentTime = 0
        duration = 0
        currentSong = nil
        playQueue = []
        currentIndex = 0
        
        if let timeObserver = timeObserver {
            player?.removeTimeObserver(timeObserver)
            self.timeObserver = nil
        }
        
        updateNowPlayingInfo()
    }
    
    func next() {
        guard !playQueue.isEmpty else { return }
        
        let nextIndex = getNextIndex()
        if nextIndex != currentIndex {
            currentIndex = nextIndex
            currentSong = playQueue[currentIndex]
            loadAndPlayCurrentSong()
        }
    }
    
    func previous() {
        guard !playQueue.isEmpty else { return }
        
        // 如果播放时间超过3秒，重新开始当前歌曲
        if currentTime > 3.0 {
            seek(to: 0)
            return
        }
        
        let previousIndex = getPreviousIndex()
        if previousIndex != currentIndex {
            currentIndex = previousIndex
            currentSong = playQueue[currentIndex]
            loadAndPlayCurrentSong()
        }
    }
    
    func seek(to time: TimeInterval) {
        let cmTime = CMTime(seconds: time, preferredTimescale: 1000)
        player?.seek(to: cmTime) { [weak self] _ in
            DispatchQueue.main.async {
                self?.currentTime = time
                self?.updateNowPlayingInfo()
            }
        }
    }
    
    func setVolume(_ volume: Float) {
        self.volume = max(0.0, min(1.0, volume))
        player?.volume = self.volume
    }
    
    func setPlayMode(_ mode: PlayMode) {
        self.playMode = mode
    }
    
    // MARK: - Private Methods
    private func loadAndPlayCurrentSong() {
        guard let song = currentSong else { return }
        
        isLoading = true
        
        // 检查本地缓存
        if song.isLocal, let localPath = song.localPath {
            let url = URL(fileURLWithPath: localPath)
            setupPlayer(with: url)
        } else {
            // 从OSS下载或流式播放
            downloadAndPlay(song: song)
        }
    }
    
    private func downloadAndPlay(song: Song) {
        guard let cacheManager = cacheManager else {
            isLoading = false
            return
        }

        // 尝试从缓存获取
        cacheManager.getCachedSongURL(for: song)
            .sink(
                receiveCompletion: { [weak self] completion in
                    if case .failure(let error) = completion {
                        print("Failed to get cached song: \(error)")
                        self?.isLoading = false
                        self?.handlePlaybackError()
                    }
                },
                receiveValue: { [weak self] url in
                    if let url = url {
                        self?.setupPlayer(with: url)
                    } else {
                        // 开始下载
                        self?.startDownload(song: song)
                    }
                }
            )
            .store(in: &cancellables)
    }

    private func startDownload(song: Song) {
        guard let cacheManager = cacheManager else {
            isLoading = false
            return
        }

        cacheManager.downloadSong(song)
            .sink(
                receiveCompletion: { [weak self] completion in
                    if case .failure(let error) = completion {
                        print("Failed to download song: \(error)")
                        self?.isLoading = false
                        self?.handlePlaybackError()
                    }
                },
                receiveValue: { [weak self] url in
                    self?.setupPlayer(with: url)
                }
            )
            .store(in: &cancellables)
    }
    
    private func setupPlayer(with url: URL) {
        let playerItem = AVPlayerItem(url: url)
        player = AVPlayer(playerItem: playerItem)
        
        // 设置音量
        player?.volume = volume
        
        // 添加时间观察器
        setupTimeObserver()
        
        // 监听播放项状态
        playerItem.publisher(for: \.status)
            .sink { [weak self] status in
                DispatchQueue.main.async {
                    switch status {
                    case .readyToPlay:
                        self?.isLoading = false
                        self?.duration = playerItem.duration.seconds
                        self?.player?.play()
                        self?.isPlaying = true
                        self?.updateNowPlayingInfo()
                        self?.markSongAsPlayed()
                    case .failed:
                        self?.isLoading = false
                        self?.handlePlaybackError()
                    default:
                        break
                    }
                }
            }
            .store(in: &cancellables)
    }
    
    private func setupTimeObserver() {
        let interval = CMTime(seconds: 0.5, preferredTimescale: 1000)
        timeObserver = player?.addPeriodicTimeObserver(forInterval: interval, queue: .main) { [weak self] time in
            self?.currentTime = time.seconds
            self?.updateNowPlayingInfo()
        }
    }
    
    private func getNextIndex() -> Int {
        switch playMode {
        case .sequential:
            return currentIndex < playQueue.count - 1 ? currentIndex + 1 : 0
        case .shuffle:
            return Int.random(in: 0..<playQueue.count)
        case .repeatOne:
            return currentIndex
        case .repeatAll:
            return currentIndex < playQueue.count - 1 ? currentIndex + 1 : 0
        }
    }
    
    private func getPreviousIndex() -> Int {
        switch playMode {
        case .sequential, .repeatAll:
            return currentIndex > 0 ? currentIndex - 1 : playQueue.count - 1
        case .shuffle:
            return Int.random(in: 0..<playQueue.count)
        case .repeatOne:
            return currentIndex
        }
    }
    
    private func markSongAsPlayed() {
        guard let song = currentSong else { return }
        
        songRepository?.markAsPlayed(song, duration: 0.0)
            .sink(
                receiveCompletion: { completion in
                    if case .failure(let error) = completion {
                        print("Failed to mark song as played: \(error)")
                    }
                },
                receiveValue: { _ in
                    print("Song marked as played: \(song.title)")
                }
            )
            .store(in: &cancellables)
    }
    
    private func updateNowPlayingInfo() {
        guard let song = currentSong else {
            MPNowPlayingInfoCenter.default().nowPlayingInfo = nil
            return
        }
        
        var nowPlayingInfo: [String: Any] = [
            MPMediaItemPropertyTitle: song.title,
            MPMediaItemPropertyArtist: song.displayArtist,
            MPMediaItemPropertyAlbumTitle: song.displayAlbum,
            MPMediaItemPropertyPlaybackDuration: duration,
            MPNowPlayingInfoPropertyElapsedPlaybackTime: currentTime,
            MPNowPlayingInfoPropertyPlaybackRate: isPlaying ? 1.0 : 0.0
        ]
        
        if let artworkImage = song.albumArtworkImage {
            let artwork = MPMediaItemArtwork(boundsSize: artworkImage.size) { _ in
                return artworkImage
            }
            nowPlayingInfo[MPMediaItemPropertyArtwork] = artwork
        }
        
        MPNowPlayingInfoCenter.default().nowPlayingInfo = nowPlayingInfo
    }
    
    private func handlePlaybackError() {
        print("Playback error occurred")
        // TODO: 实现错误处理逻辑
        next() // 跳到下一首
    }
    
    // MARK: - Notification Handlers
    @objc private func playerDidFinishPlaying() {
        if playMode == .repeatOne {
            seek(to: 0)
            player?.play()
        } else {
            next()
        }
    }
    
    @objc private func playerItemFailedToPlay() {
        handlePlaybackError()
    }
    
    @objc private func audioSessionInterrupted(notification: Notification) {
        guard let userInfo = notification.userInfo,
              let typeValue = userInfo[AVAudioSessionInterruptionTypeKey] as? UInt,
              let type = AVAudioSession.InterruptionType(rawValue: typeValue) else {
            return
        }
        
        switch type {
        case .began:
            pause()
        case .ended:
            if let optionsValue = userInfo[AVAudioSessionInterruptionOptionKey] as? UInt {
                let options = AVAudioSession.InterruptionOptions(rawValue: optionsValue)
                if options.contains(.shouldResume) {
                    resume()
                }
            }
        @unknown default:
            break
        }
    }

    // MARK: - Queue Management
    func addToQueue(_ song: Song) {
        playQueue.append(song)
    }

    func addToQueue(_ songs: [Song]) {
        playQueue.append(contentsOf: songs)
    }

    func removeFromQueue(_ song: Song) {
        playQueue.removeAll { $0.objectID == song.objectID }
    }

    func removeFromQueue(at index: Int) {
        guard index < playQueue.count else { return }
        playQueue.remove(at: index)
    }

    func moveInQueue(from source: IndexSet, to destination: Int) {
        playQueue.move(fromOffsets: source, toOffset: destination)
    }

    func shuffleQueue() {
        // 保持当前播放歌曲在队列开头
        if let current = currentSong,
           let currentIndex = playQueue.firstIndex(where: { $0.objectID == current.objectID }) {
            playQueue.remove(at: currentIndex)
            playQueue.shuffle()
            playQueue.insert(current, at: 0)
            self.currentIndex = 0
        } else {
            playQueue.shuffle()
        }
    }

    func playAtIndex(_ index: Int) {
        guard index < playQueue.count else { return }
        currentIndex = index
        currentSong = playQueue[index]
        play(song: currentSong!)
    }

    func clearQueue() {
        playQueue.removeAll()
        currentSong = nil
        stop()
    }
}

// MARK: - Play Mode Enum
enum PlayMode: String, CaseIterable {
    case sequential = "sequential"
    case shuffle = "shuffle"
    case repeatOne = "repeatOne"
    case repeatAll = "repeatAll"
    
    var iconName: String {
        switch self {
        case .sequential: return "arrow.right"
        case .shuffle: return "shuffle"
        case .repeatOne: return "repeat.1"
        case .repeatAll: return "repeat"
        }
    }
    
    var displayName: String {
        switch self {
        case .sequential: return "顺序播放"
        case .shuffle: return "随机播放"
        case .repeatOne: return "单曲循环"
        case .repeatAll: return "列表循环"
        }
    }
}
