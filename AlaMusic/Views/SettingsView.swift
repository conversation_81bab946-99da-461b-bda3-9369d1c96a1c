import SwiftUI

struct SettingsView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var viewModel = SettingsViewModel()
    @State private var showingOSSConfig = false
    @State private var showingCacheSettings = false
    
    var body: some View {
        NavigationView {
            List {
                // 云存储设置
                Section("云存储") {
                    HStack {
                        Image(systemName: "icloud")
                            .foregroundColor(.blue)
                            .frame(width: 30)
                        
                        VStack(alignment: .leading, spacing: 4) {
                            Text("阿里云OSS")
                                .font(.headline)
                            
                            Text(viewModel.isOSSConfigured ? "已配置" : "未配置")
                                .font(.caption)
                                .foregroundColor(viewModel.isOSSConfigured ? .green : .secondary)
                        }
                        
                        Spacer()
                        
                        Button("配置") {
                            showingOSSConfig = true
                        }
                        .buttonStyle(.bordered)
                    }
                    
                    if viewModel.isOSSConfigured {
                        HStack {
                            Image(systemName: "wifi")
                                .foregroundColor(.green)
                                .frame(width: 30)
                            
                            Text("连接状态")
                            
                            Spacer()
                            
                            Text(viewModel.connectionStatus)
                                .foregroundColor(viewModel.isConnected ? .green : .red)
                        }
                    }
                }
                
                // 播放设置
                Section("播放") {
                    HStack {
                        Image(systemName: "speaker.wave.2")
                            .foregroundColor(.orange)
                            .frame(width: 30)
                        
                        Text("音质")
                        
                        Spacer()
                        
                        Picker("音质", selection: $viewModel.audioQuality) {
                            Text("标准").tag(AudioQuality.standard)
                            Text("高品质").tag(AudioQuality.high)
                            Text("无损").tag(AudioQuality.lossless)
                        }
                        .pickerStyle(MenuPickerStyle())
                    }
                    
                    Toggle(isOn: $viewModel.enableCrossfade) {
                        HStack {
                            Image(systemName: "waveform")
                                .foregroundColor(.purple)
                                .frame(width: 30)
                            
                            Text("淡入淡出")
                        }
                    }
                    
                    Toggle(isOn: $viewModel.enableEqualizer) {
                        HStack {
                            Image(systemName: "slider.horizontal.3")
                                .foregroundColor(.pink)
                                .frame(width: 30)
                            
                            Text("均衡器")
                        }
                    }
                }
                
                // 缓存设置
                Section("缓存") {
                    HStack {
                        Image(systemName: "externaldrive")
                            .foregroundColor(.gray)
                            .frame(width: 30)
                        
                        VStack(alignment: .leading, spacing: 4) {
                            Text("缓存大小")
                                .font(.headline)
                            
                            Text(viewModel.cacheUsageText)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        Spacer()
                        
                        Button("管理") {
                            showingCacheSettings = true
                        }
                        .buttonStyle(.bordered)
                    }
                    
                    HStack {
                        Image(systemName: "gear")
                            .foregroundColor(.blue)
                            .frame(width: 30)
                        
                        Text("最大缓存大小")
                        
                        Spacer()
                        
                        Picker("缓存大小", selection: $viewModel.maxCacheSize) {
                            Text("1GB").tag(CacheSize.oneGB)
                            Text("2GB").tag(CacheSize.twoGB)
                            Text("5GB").tag(CacheSize.fiveGB)
                            Text("10GB").tag(CacheSize.tenGB)
                        }
                        .pickerStyle(MenuPickerStyle())
                    }
                    
                    Toggle(isOn: $viewModel.enableAutoCache) {
                        HStack {
                            Image(systemName: "arrow.down.circle")
                                .foregroundColor(.green)
                                .frame(width: 30)
                            
                            Text("自动缓存")
                        }
                    }
                }
                
                // 关于
                Section("关于") {
                    HStack {
                        Image(systemName: "info.circle")
                            .foregroundColor(.blue)
                            .frame(width: 30)
                        
                        Text("版本")
                        
                        Spacer()
                        
                        Text(viewModel.appVersion)
                            .foregroundColor(.secondary)
                    }
                    
                    HStack {
                        Image(systemName: "envelope")
                            .foregroundColor(.orange)
                            .frame(width: 30)
                        
                        Text("反馈")
                        
                        Spacer()
                        
                        Image(systemName: "chevron.right")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .contentShape(Rectangle())
                    .onTapGesture {
                        // 打开反馈页面
                    }
                }
            }
            .navigationTitle("设置")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        dismiss()
                    }
                }
            }
            .sheet(isPresented: $showingOSSConfig) {
                OSSConfigView()
            }
            .sheet(isPresented: $showingCacheSettings) {
                CacheSettingsView()
            }
            .onAppear {
                viewModel.loadSettings()
            }
        }
    }
}

// MARK: - Settings ViewModel
@MainActor
class SettingsViewModel: ObservableObject {
    @Published var isOSSConfigured = false
    @Published var isConnected = false
    @Published var connectionStatus = "未连接"
    @Published var audioQuality: AudioQuality = .standard
    @Published var enableCrossfade = false
    @Published var enableEqualizer = false
    @Published var cacheUsageText = "0 MB / 1 GB"
    @Published var maxCacheSize: CacheSize = .oneGB
    @Published var enableAutoCache = true
    @Published var appVersion = "1.0.0"
    
    private let cloudStorageManager = CloudStorageManager.shared
    private let cacheManager = CacheManager.shared
    
    func loadSettings() {
        // 加载云存储状态
        isOSSConfigured = UserDefaults.standard.data(forKey: "OSSConfig") != nil
        isConnected = cloudStorageManager.isConnected
        connectionStatus = isConnected ? "已连接" : "未连接"
        
        // 加载播放设置
        if let qualityRaw = UserDefaults.standard.object(forKey: "AudioQuality") as? Int,
           let quality = AudioQuality(rawValue: qualityRaw) {
            audioQuality = quality
        }
        
        enableCrossfade = UserDefaults.standard.bool(forKey: "EnableCrossfade")
        enableEqualizer = UserDefaults.standard.bool(forKey: "EnableEqualizer")
        
        // 加载缓存设置
        if let cacheSizeRaw = UserDefaults.standard.object(forKey: "MaxCacheSize") as? Int,
           let cacheSize = CacheSize(rawValue: cacheSizeRaw) {
            maxCacheSize = cacheSize
        }
        
        enableAutoCache = UserDefaults.standard.bool(forKey: "EnableAutoCache")
        
        // 更新缓存使用情况
        updateCacheUsage()
        
        // 获取应用版本
        if let version = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String {
            appVersion = version
        }
    }
    
    private func updateCacheUsage() {
        let usage = cacheManager.getCacheUsage()
        let maxSize = maxCacheSize.bytes
        
        let usageFormatter = ByteCountFormatter()
        usageFormatter.allowedUnits = [.useMB, .useGB]
        usageFormatter.countStyle = .file
        
        let usageText = usageFormatter.string(fromByteCount: usage)
        let maxText = usageFormatter.string(fromByteCount: maxSize)
        
        cacheUsageText = "\(usageText) / \(maxText)"
    }
}

// MARK: - Enums
enum AudioQuality: Int, CaseIterable {
    case standard = 0
    case high = 1
    case lossless = 2
    
    var displayName: String {
        switch self {
        case .standard: return "标准"
        case .high: return "高品质"
        case .lossless: return "无损"
        }
    }
}

enum CacheSize: Int, CaseIterable {
    case oneGB = 1
    case twoGB = 2
    case fiveGB = 5
    case tenGB = 10
    
    var bytes: Int64 {
        return Int64(rawValue) * 1024 * 1024 * 1024
    }
    
    var displayName: String {
        return "\(rawValue)GB"
    }
}

#Preview {
    SettingsView()
}
