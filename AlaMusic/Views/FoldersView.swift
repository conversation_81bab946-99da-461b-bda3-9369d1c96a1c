import SwiftUI
import CoreData

struct FoldersView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @StateObject private var viewModel = FoldersViewModel()
    @State private var searchText = ""
    @State private var showingSettings = false
    
    var body: some View {
        NavigationView {
            VStack {
                if viewModel.isLoading {
                    LoadingView(message: "加载文件夹中...")
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                } else if viewModel.folders.isEmpty && !viewModel.hasError {
                    EmptyFoldersView()
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                } else {
                    List {
                        ForEach(viewModel.filteredFolders) { folder in
                            NavigationLink(destination: FolderDetailView(folder: folder)) {
                                FolderRow(folder: folder)
                            }
                        }
                    }
                    .listStyle(PlainListStyle())
                    .searchable(text: $searchText, prompt: "搜索文件夹")
                    .refreshable {
                        await viewModel.refresh()
                    }
                }
            }
            .navigationTitle("目录")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: { showingSettings = true }) {
                        Image(systemName: "gear")
                    }
                }
            }
            .sheet(isPresented: $showingSettings) {
                SettingsView()
            }
            .onAppear {
                viewModel.setContext(viewContext)
                Task {
                    await viewModel.loadFolders()
                }
            }
            .onChange(of: searchText) { newValue in
                viewModel.searchFolders(query: newValue)
            }
        }
        .alert("错误", isPresented: .constant(viewModel.errorMessage != nil)) {
            Button("确定") {
                viewModel.errorMessage = nil
            }
        } message: {
            if let errorMessage = viewModel.errorMessage {
                Text(errorMessage)
            }
        }
    }
}

// MARK: - Folder Row
struct FolderRow: View {
    let folder: CloudFolder
    
    var body: some View {
        HStack(spacing: 12) {
            // 文件夹图标
            Image(systemName: "folder.fill")
                .font(.title2)
                .foregroundColor(.accentColor)
                .frame(width: 40, height: 40)
                .background(Color.accentColor.opacity(0.1))
                .clipShape(RoundedRectangle(cornerRadius: 8))
            
            // 文件夹信息
            VStack(alignment: .leading, spacing: 4) {
                Text(folder.displayName)
                    .font(.headline)
                    .foregroundColor(.primary)
                    .lineLimit(1)
                
                HStack {
                    if folder.fileCount > 0 {
                        Text("\(folder.fileCount) 个文件")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    if let modifiedDate = folder.modifiedDate {
                        if folder.fileCount > 0 {
                            Text("•")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        Text(modifiedDate, style: .relative)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
            
            Spacer()
            
            // 箭头
            Image(systemName: "chevron.right")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding(.vertical, 4)
    }
}

// MARK: - Empty Folders View
struct EmptyFoldersView: View {
    var body: some View {
        VStack(spacing: 24) {
            Image(systemName: "folder")
                .font(.system(size: 60))
                .foregroundColor(.secondary)
            
            VStack(spacing: 8) {
                Text("暂无文件夹")
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
                
                Text("请检查云存储配置或上传音乐文件")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            
            Button("检查设置") {
                // 这里可以导航到设置页面
            }
            .buttonStyle(.borderedProminent)
        }
        .padding()
    }
}

// MARK: - Folders ViewModel
@MainActor
class FoldersViewModel: ObservableObject {
    @Published var folders: [CloudFolder] = []
    @Published var filteredFolders: [CloudFolder] = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var hasError = false
    
    private var context: NSManagedObjectContext?
    private var cloudStorageManager: CloudStorageManager?
    
    func setContext(_ context: NSManagedObjectContext) {
        self.context = context
        self.cloudStorageManager = CloudStorageManager.shared
    }
    
    func loadFolders() async {
        guard let cloudStorageManager = cloudStorageManager else { return }
        
        isLoading = true
        errorMessage = nil
        hasError = false
        
        do {
            let loadedFolders = try await cloudStorageManager.listFolders()
            folders = loadedFolders
            filteredFolders = loadedFolders
        } catch {
            errorMessage = "加载文件夹失败: \(error.localizedDescription)"
            hasError = true
            folders = []
            filteredFolders = []
        }
        
        isLoading = false
    }
    
    func refresh() async {
        await loadFolders()
    }
    
    func searchFolders(query: String) {
        let trimmedQuery = query.trimmingCharacters(in: .whitespacesAndNewlines).lowercased()
        
        if trimmedQuery.isEmpty {
            filteredFolders = folders
        } else {
            filteredFolders = folders.filter { folder in
                folder.name.lowercased().contains(trimmedQuery)
            }
        }
    }
}

// MARK: - Cloud Folder Model
struct CloudFolder: Identifiable, Hashable {
    let id = UUID()
    let name: String
    let path: String
    let fileCount: Int
    let modifiedDate: Date?
    
    var displayName: String {
        return name.isEmpty ? "根目录" : name
    }
}

#Preview {
    FoldersView()
        .environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
}
