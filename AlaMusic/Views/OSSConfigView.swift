import SwiftUI

struct OSSConfigView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var viewModel = OSSConfigViewModel()
    
    var body: some View {
        NavigationView {
            Form {
                Section {
                    TextField("Endpoint", text: $viewModel.endpoint)
                        .textContentType(.URL)
                        .autocapitalization(.none)
                        .disableAutocorrection(true)
                    
                    TextField("Access Key ID", text: $viewModel.accessKeyId)
                        .textContentType(.username)
                        .autocapitalization(.none)
                        .disableAutocorrection(true)
                    
                    SecureField("Access Key Secret", text: $viewModel.accessKeySecret)
                        .textContentType(.password)
                    
                    TextField("Bucket Name", text: $viewModel.bucketName)
                        .autocapitalization(.none)
                        .disableAutocorrection(true)
                    
                    TextField("Security Token (可选)", text: $viewModel.securityToken)
                        .autocapitalization(.none)
                        .disableAutocorrection(true)
                } header: {
                    Text("阿里云OSS配置")
                } footer: {
                    Text("请填写您的阿里云OSS访问凭证。这些信息将安全地存储在本地。")
                }
                
                Section {
                    Button(action: {
                        Task {
                            await viewModel.testConnection()
                        }
                    }) {
                        HStack {
                            if viewModel.isTesting {
                                ProgressView()
                                    .scaleEffect(0.8)
                                Text("测试连接中...")
                            } else {
                                Image(systemName: "wifi")
                                Text("测试连接")
                            }
                        }
                    }
                    .disabled(viewModel.isTesting || !viewModel.isFormValid)
                    
                    if let testResult = viewModel.testResult {
                        HStack {
                            Image(systemName: testResult.isSuccess ? "checkmark.circle.fill" : "xmark.circle.fill")
                                .foregroundColor(testResult.isSuccess ? .green : .red)
                            
                            Text(testResult.message)
                                .foregroundColor(testResult.isSuccess ? .green : .red)
                        }
                    }
                } header: {
                    Text("连接测试")
                }
            }
            .navigationTitle("OSS配置")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("保存") {
                        viewModel.saveConfiguration()
                        dismiss()
                    }
                    .disabled(!viewModel.isFormValid || (viewModel.testResult?.isSuccess != true))
                }
            }
            .onAppear {
                viewModel.loadExistingConfiguration()
            }
        }
    }
}

// MARK: - OSS Config ViewModel
@MainActor
class OSSConfigViewModel: ObservableObject {
    @Published var endpoint = ""
    @Published var accessKeyId = ""
    @Published var accessKeySecret = ""
    @Published var bucketName = ""
    @Published var securityToken = ""
    @Published var isTesting = false
    @Published var testResult: TestResult?
    
    var isFormValid: Bool {
        !endpoint.isEmpty && !accessKeyId.isEmpty && !accessKeySecret.isEmpty && !bucketName.isEmpty
    }
    
    func loadExistingConfiguration() {
        if let configData = UserDefaults.standard.data(forKey: "OSSConfig"),
           let config = try? JSONDecoder().decode(OSSConfig.self, from: configData) {
            endpoint = config.endpoint
            accessKeyId = config.accessKeyId
            accessKeySecret = config.accessKeySecret
            bucketName = config.bucketName
            securityToken = config.securityToken ?? ""
        }
    }
    
    func testConnection() async {
        guard isFormValid else { return }
        
        isTesting = true
        testResult = nil
        
        let config = OSSConfig(
            endpoint: endpoint,
            accessKeyId: accessKeyId,
            accessKeySecret: accessKeySecret,
            bucketName: bucketName,
            securityToken: securityToken.isEmpty ? nil : securityToken
        )
        
        do {
            let ossClient = OSSClient(config: config)
            _ = try await ossClient.listBuckets()
            testResult = TestResult(isSuccess: true, message: "连接成功")
        } catch {
            testResult = TestResult(isSuccess: false, message: "连接失败: \(error.localizedDescription)")
        }
        
        isTesting = false
    }
    
    func saveConfiguration() {
        guard isFormValid else { return }
        
        let config = OSSConfig(
            endpoint: endpoint,
            accessKeyId: accessKeyId,
            accessKeySecret: accessKeySecret,
            bucketName: bucketName,
            securityToken: securityToken.isEmpty ? nil : securityToken
        )
        
        if let configData = try? JSONEncoder().encode(config) {
            UserDefaults.standard.set(configData, forKey: "OSSConfig")
            
            // 更新CloudStorageManager配置
            CloudStorageManager.shared.configure(with: config)
        }
    }
}

// MARK: - Test Result
struct TestResult {
    let isSuccess: Bool
    let message: String
}

#Preview {
    OSSConfigView()
}
