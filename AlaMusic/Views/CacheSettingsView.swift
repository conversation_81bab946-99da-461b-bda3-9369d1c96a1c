import SwiftUI

struct CacheSettingsView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var viewModel = CacheSettingsViewModel()
    @State private var showingClearConfirmation = false
    
    var body: some View {
        NavigationView {
            List {
                // 缓存统计
                Section("缓存统计") {
                    HStack {
                        Image(systemName: "externaldrive")
                            .foregroundColor(.blue)
                            .frame(width: 30)
                        
                        VStack(alignment: .leading, spacing: 4) {
                            Text("已使用")
                                .font(.headline)
                            
                            Text(viewModel.usedCacheText)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        Spacer()
                        
                        CircularProgressView(
                            progress: viewModel.cacheUsagePercentage,
                            color: viewModel.cacheUsageColor
                        )
                        .frame(width: 40, height: 40)
                    }
                    
                    HStack {
                        Image(systemName: "music.note")
                            .foregroundColor(.orange)
                            .frame(width: 30)
                        
                        Text("缓存文件数")
                        
                        Spacer()
                        
                        Text("\(viewModel.cachedFilesCount)")
                            .foregroundColor(.secondary)
                    }
                    
                    HStack {
                        Image(systemName: "clock")
                            .foregroundColor(.green)
                            .frame(width: 30)
                        
                        Text("最后清理时间")
                        
                        Spacer()
                        
                        Text(viewModel.lastCleanupText)
                            .foregroundColor(.secondary)
                    }
                }
                
                // 缓存管理
                Section("缓存管理") {
                    Button(action: {
                        Task {
                            await viewModel.cleanupOldFiles()
                        }
                    }) {
                        HStack {
                            Image(systemName: "trash")
                                .foregroundColor(.orange)
                                .frame(width: 30)
                            
                            Text("清理过期文件")
                            
                            Spacer()
                            
                            if viewModel.isCleaningUp {
                                ProgressView()
                                    .scaleEffect(0.8)
                            }
                        }
                    }
                    .disabled(viewModel.isCleaningUp)
                    
                    Button(action: {
                        showingClearConfirmation = true
                    }) {
                        HStack {
                            Image(systemName: "trash.fill")
                                .foregroundColor(.red)
                                .frame(width: 30)
                            
                            Text("清空所有缓存")
                                .foregroundColor(.red)
                            
                            Spacer()
                        }
                    }
                    .disabled(viewModel.isCleaningUp)
                }
                
                // 缓存设置
                Section("缓存设置") {
                    HStack {
                        Image(systemName: "gear")
                            .foregroundColor(.blue)
                            .frame(width: 30)
                        
                        Text("最大缓存大小")
                        
                        Spacer()
                        
                        Picker("缓存大小", selection: $viewModel.maxCacheSize) {
                            ForEach(CacheSize.allCases, id: \.self) { size in
                                Text(size.displayName).tag(size)
                            }
                        }
                        .pickerStyle(MenuPickerStyle())
                    }
                    
                    HStack {
                        Image(systemName: "timer")
                            .foregroundColor(.purple)
                            .frame(width: 30)
                        
                        Text("自动清理周期")
                        
                        Spacer()
                        
                        Picker("清理周期", selection: $viewModel.cleanupInterval) {
                            Text("每天").tag(CleanupInterval.daily)
                            Text("每周").tag(CleanupInterval.weekly)
                            Text("每月").tag(CleanupInterval.monthly)
                            Text("手动").tag(CleanupInterval.manual)
                        }
                        .pickerStyle(MenuPickerStyle())
                    }
                    
                    Toggle(isOn: $viewModel.enableSmartCleanup) {
                        HStack {
                            Image(systemName: "brain")
                                .foregroundColor(.pink)
                                .frame(width: 30)
                            
                            VStack(alignment: .leading, spacing: 2) {
                                Text("智能清理")
                                Text("根据播放频率自动清理")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                    }
                }
                
                // 缓存文件列表
                if !viewModel.cachedFiles.isEmpty {
                    Section("缓存文件") {
                        ForEach(viewModel.cachedFiles.prefix(10), id: \.path) { file in
                            CachedFileRow(file: file) {
                                Task {
                                    await viewModel.removeCachedFile(file)
                                }
                            }
                        }
                        
                        if viewModel.cachedFiles.count > 10 {
                            Text("还有 \(viewModel.cachedFiles.count - 10) 个文件...")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
            }
            .navigationTitle("缓存管理")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        dismiss()
                    }
                }
            }
            .onAppear {
                Task {
                    await viewModel.loadCacheInfo()
                }
            }
            .onChange(of: viewModel.maxCacheSize) { _ in
                viewModel.saveSettings()
            }
            .onChange(of: viewModel.cleanupInterval) { _ in
                viewModel.saveSettings()
            }
            .onChange(of: viewModel.enableSmartCleanup) { _ in
                viewModel.saveSettings()
            }
            .alert("清空缓存", isPresented: $showingClearConfirmation) {
                Button("取消", role: .cancel) { }
                Button("清空", role: .destructive) {
                    Task {
                        await viewModel.clearAllCache()
                    }
                }
            } message: {
                Text("这将删除所有缓存文件，确定要继续吗？")
            }
        }
    }
}

// MARK: - Cached File Row
struct CachedFileRow: View {
    let file: CachedFileInfo
    let onRemove: () -> Void
    
    var body: some View {
        HStack {
            Image(systemName: "music.note")
                .foregroundColor(.accentColor)
                .frame(width: 30)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(file.name)
                    .font(.subheadline)
                    .lineLimit(1)
                
                HStack {
                    Text(file.sizeText)
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text("•")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text(file.lastAccessedText)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            Spacer()
            
            Button(action: onRemove) {
                Image(systemName: "trash")
                    .foregroundColor(.red)
            }
            .buttonStyle(PlainButtonStyle())
        }
    }
}

// MARK: - Circular Progress View
struct CircularProgressView: View {
    let progress: Double
    let color: Color
    
    var body: some View {
        ZStack {
            Circle()
                .stroke(color.opacity(0.2), lineWidth: 4)
            
            Circle()
                .trim(from: 0, to: progress)
                .stroke(color, style: StrokeStyle(lineWidth: 4, lineCap: .round))
                .rotationEffect(.degrees(-90))
            
            Text("\(Int(progress * 100))%")
                .font(.caption2)
                .fontWeight(.semibold)
        }
    }
}

// MARK: - Cache Settings ViewModel
@MainActor
class CacheSettingsViewModel: ObservableObject {
    @Published var usedCacheText = "0 MB"
    @Published var cacheUsagePercentage: Double = 0.0
    @Published var cacheUsageColor: Color = .green
    @Published var cachedFilesCount = 0
    @Published var lastCleanupText = "从未"
    @Published var isCleaningUp = false
    @Published var maxCacheSize: CacheSize = .oneGB
    @Published var cleanupInterval: CleanupInterval = .weekly
    @Published var enableSmartCleanup = true
    @Published var cachedFiles: [CachedFileInfo] = []

    private let cacheManager = CacheManager.shared

    func loadCacheInfo() async {
        let usage = cacheManager.getCacheUsage()
        let maxSize = maxCacheSize.bytes

        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useMB, .useGB]
        formatter.countStyle = .file

        usedCacheText = formatter.string(fromByteCount: usage)
        cacheUsagePercentage = Double(usage) / Double(maxSize)

        // 设置颜色
        if cacheUsagePercentage < 0.7 {
            cacheUsageColor = .green
        } else if cacheUsagePercentage < 0.9 {
            cacheUsageColor = .orange
        } else {
            cacheUsageColor = .red
        }

        cachedFilesCount = cacheManager.getCachedFilesCount()

        // 加载设置
        loadSettings()

        // 加载缓存文件列表
        cachedFiles = cacheManager.getCachedFilesList()

        // 最后清理时间
        if let lastCleanup = UserDefaults.standard.object(forKey: "LastCacheCleanup") as? Date {
            let formatter = RelativeDateTimeFormatter()
            lastCleanupText = formatter.localizedString(for: lastCleanup, relativeTo: Date())
        }
    }

    func cleanupOldFiles() async {
        isCleaningUp = true

        do {
            try await cacheManager.cleanupOldFiles()
            UserDefaults.standard.set(Date(), forKey: "LastCacheCleanup")
            await loadCacheInfo()
        } catch {
            // 处理错误
        }

        isCleaningUp = false
    }

    func clearAllCache() async {
        isCleaningUp = true

        do {
            try await cacheManager.clearAllCache()
            await loadCacheInfo()
        } catch {
            // 处理错误
        }

        isCleaningUp = false
    }

    func removeCachedFile(_ file: CachedFileInfo) async {
        cacheManager.removeCachedFile(key: file.path)
        await loadCacheInfo()
    }

    func saveSettings() {
        UserDefaults.standard.set(maxCacheSize.rawValue, forKey: "MaxCacheSize")
        UserDefaults.standard.set(cleanupInterval.rawValue, forKey: "CleanupInterval")
        UserDefaults.standard.set(enableSmartCleanup, forKey: "EnableSmartCleanup")
    }

    private func loadSettings() {
        if let cacheSizeRaw = UserDefaults.standard.object(forKey: "MaxCacheSize") as? Int,
           let cacheSize = CacheSize(rawValue: cacheSizeRaw) {
            maxCacheSize = cacheSize
        }

        if let intervalRaw = UserDefaults.standard.object(forKey: "CleanupInterval") as? Int,
           let interval = CleanupInterval(rawValue: intervalRaw) {
            cleanupInterval = interval
        }

        enableSmartCleanup = UserDefaults.standard.bool(forKey: "EnableSmartCleanup")
    }
}

// MARK: - Cleanup Interval
enum CleanupInterval: Int, CaseIterable {
    case daily = 1
    case weekly = 7
    case monthly = 30
    case manual = 0

    var displayName: String {
        switch self {
        case .daily: return "每天"
        case .weekly: return "每周"
        case .monthly: return "每月"
        case .manual: return "手动"
        }
    }
}

// MARK: - Cached File Info
struct CachedFileInfo {
    let name: String
    let path: String
    let size: Int64
    let lastAccessed: Date

    var sizeText: String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useKB, .useMB]
        formatter.countStyle = .file
        return formatter.string(fromByteCount: size)
    }

    var lastAccessedText: String {
        let formatter = RelativeDateTimeFormatter()
        return formatter.localizedString(for: lastAccessed, relativeTo: Date())
    }
}

#Preview {
    CacheSettingsView()
}
