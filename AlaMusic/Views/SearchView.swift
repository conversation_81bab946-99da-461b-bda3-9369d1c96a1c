import SwiftUI
import CoreData

struct SearchView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @StateObject private var viewModel = SearchViewModel()
    @State private var searchText = ""
    @State private var selectedScope: SearchScope = .all
    
    var body: some View {
        NavigationView {
            VStack {
                // 搜索范围选择器
                Picker("搜索范围", selection: $selectedScope) {
                    ForEach(SearchScope.allCases, id: \.self) { scope in
                        Text(scope.displayName).tag(scope)
                    }
                }
                .pickerStyle(SegmentedPickerStyle())
                .padding(.horizontal)
                
                if searchText.isEmpty {
                    SearchEmptyView()
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                } else if viewModel.isSearching {
                    LoadingView(message: "搜索中...")
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                } else if viewModel.searchResults.isEmpty {
                    SearchNoResultsView(query: searchText)
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                } else {
                    SearchResultsList(
                        results: viewModel.searchResults,
                        onSongTap: { song in
                            playSearchResult(song)
                        },
                        onSongListTap: { songList in
                            // 导航到歌单详情
                        },
                        onAddToPlaylist: { song in
                            // 添加到歌单
                        }
                    )
                }
            }
            .navigationTitle("搜索")
            .navigationBarTitleDisplayMode(.large)
            .searchable(text: $searchText, prompt: "搜索歌曲、歌单或艺术家")
            .onAppear {
                viewModel.setContext(viewContext)
            }
            .onChange(of: searchText) { newValue in
                Task {
                    await viewModel.search(query: newValue, scope: selectedScope)
                }
            }
            .onChange(of: selectedScope) { newScope in
                if !searchText.isEmpty {
                    Task {
                        await viewModel.search(query: searchText, scope: newScope)
                    }
                }
            }
        }
    }
    
    private func playSearchResult(_ song: Song) {
        let audioPlayerManager = AudioPlayerManager.shared
        audioPlayerManager.setPlayQueue([song])
        audioPlayerManager.playAtIndex(0)
    }
}

// MARK: - Search Results List
struct SearchResultsList: View {
    let results: [SearchResult]
    let onSongTap: (Song) -> Void
    let onSongListTap: (SongList) -> Void
    let onAddToPlaylist: (Song) -> Void
    
    var body: some View {
        List {
            ForEach(results, id: \.id) { result in
                switch result {
                case .song(let song):
                    SearchSongRow(
                        song: song,
                        onTap: { onSongTap(song) },
                        onAddToPlaylist: { onAddToPlaylist(song) }
                    )
                    
                case .songList(let songList):
                    SearchSongListRow(
                        songList: songList,
                        onTap: { onSongListTap(songList) }
                    )
                    
                case .artist(let artist):
                    SearchArtistRow(artist: artist)
                    
                case .album(let album):
                    SearchAlbumRow(album: album)
                }
            }
        }
        .listStyle(PlainListStyle())
    }
}

// MARK: - Search Song Row
struct SearchSongRow: View {
    let song: Song
    let onTap: () -> Void
    let onAddToPlaylist: () -> Void
    
    var body: some View {
        HStack(spacing: 12) {
            // 专辑封面
            AsyncImage(url: song.albumArtURL) { image in
                image
                    .resizable()
                    .aspectRatio(contentMode: .fill)
            } placeholder: {
                RoundedRectangle(cornerRadius: 6)
                    .fill(Color.accentColor.opacity(0.2))
                    .overlay(
                        Image(systemName: "music.note")
                            .font(.caption)
                            .foregroundColor(.accentColor)
                    )
            }
            .frame(width: 50, height: 50)
            .clipShape(RoundedRectangle(cornerRadius: 6))
            
            // 歌曲信息
            VStack(alignment: .leading, spacing: 4) {
                Text(song.displayTitle)
                    .font(.headline)
                    .foregroundColor(.primary)
                    .lineLimit(1)
                
                Text(song.displayArtist)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .lineLimit(1)
                
                if !song.displayAlbum.isEmpty && song.displayAlbum != song.displayArtist {
                    Text(song.displayAlbum)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .lineLimit(1)
                }
            }
            
            Spacer()
            
            // 操作按钮
            HStack(spacing: 8) {
                Button(action: onAddToPlaylist) {
                    Image(systemName: "plus")
                        .font(.caption)
                        .foregroundColor(.accentColor)
                }
                .buttonStyle(PlainButtonStyle())
                
                Button(action: onTap) {
                    Image(systemName: "play.fill")
                        .font(.caption)
                        .foregroundColor(.accentColor)
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
        .padding(.vertical, 4)
        .contentShape(Rectangle())
        .onTapGesture {
            onTap()
        }
    }
}

// MARK: - Search Song List Row
struct SearchSongListRow: View {
    let songList: SongList
    let onTap: () -> Void
    
    var body: some View {
        HStack(spacing: 12) {
            // 歌单图标
            Image(systemName: songList.songListType == .system ? "music.note.list" : "music.note.house")
                .font(.title2)
                .foregroundColor(.accentColor)
                .frame(width: 50, height: 50)
                .background(Color.accentColor.opacity(0.1))
                .clipShape(RoundedRectangle(cornerRadius: 6))
            
            // 歌单信息
            VStack(alignment: .leading, spacing: 4) {
                Text(songList.displayName)
                    .font(.headline)
                    .foregroundColor(.primary)
                    .lineLimit(1)
                
                Text("\(songList.songCount) 首歌曲")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            Image(systemName: "chevron.right")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding(.vertical, 4)
        .contentShape(Rectangle())
        .onTapGesture {
            onTap()
        }
    }
}

// MARK: - Search Artist Row
struct SearchArtistRow: View {
    let artist: String
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: "person.circle")
                .font(.title2)
                .foregroundColor(.orange)
                .frame(width: 50, height: 50)
                .background(Color.orange.opacity(0.1))
                .clipShape(RoundedRectangle(cornerRadius: 6))
            
            VStack(alignment: .leading, spacing: 4) {
                Text(artist)
                    .font(.headline)
                    .foregroundColor(.primary)
                    .lineLimit(1)
                
                Text("艺术家")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            Image(systemName: "chevron.right")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding(.vertical, 4)
    }
}

// MARK: - Search Album Row
struct SearchAlbumRow: View {
    let album: String
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: "opticaldisc")
                .font(.title2)
                .foregroundColor(.purple)
                .frame(width: 50, height: 50)
                .background(Color.purple.opacity(0.1))
                .clipShape(RoundedRectangle(cornerRadius: 6))
            
            VStack(alignment: .leading, spacing: 4) {
                Text(album)
                    .font(.headline)
                    .foregroundColor(.primary)
                    .lineLimit(1)
                
                Text("专辑")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            Image(systemName: "chevron.right")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding(.vertical, 4)
    }
}

// MARK: - Search Empty View
struct SearchEmptyView: View {
    var body: some View {
        VStack(spacing: 24) {
            Image(systemName: "magnifyingglass")
                .font(.system(size: 60))
                .foregroundColor(.secondary)
            
            VStack(spacing: 8) {
                Text("搜索音乐")
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
                
                Text("输入歌曲名、艺术家或专辑名开始搜索")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
        }
        .padding()
    }
}

// MARK: - Search No Results View
struct SearchNoResultsView: View {
    let query: String
    
    var body: some View {
        VStack(spacing: 24) {
            Image(systemName: "magnifyingglass")
                .font(.system(size: 60))
                .foregroundColor(.secondary)
            
            VStack(spacing: 8) {
                Text("未找到结果")
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
                
                Text("没有找到与「\(query)」相关的内容")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
        }
        .padding()
    }
}

// MARK: - Search ViewModel
@MainActor
class SearchViewModel: ObservableObject {
    @Published var searchResults: [SearchResult] = []
    @Published var isSearching = false
    @Published var errorMessage: String?

    private var context: NSManagedObjectContext?
    private var songRepository: SongRepository?
    private var songListRepository: SongListRepository?
    private var searchTask: Task<Void, Never>?

    func setContext(_ context: NSManagedObjectContext) {
        self.context = context
        self.songRepository = SongRepository(context: context)
        self.songListRepository = SongListRepository(context: context)
    }

    func search(query: String, scope: SearchScope) async {
        // 取消之前的搜索任务
        searchTask?.cancel()

        let trimmedQuery = query.trimmingCharacters(in: .whitespacesAndNewlines)

        guard !trimmedQuery.isEmpty else {
            searchResults = []
            return
        }

        searchTask = Task {
            await performSearch(query: trimmedQuery, scope: scope)
        }
    }

    private func performSearch(query: String, scope: SearchScope) async {
        guard let songRepository = songRepository,
              let songListRepository = songListRepository else { return }

        isSearching = true
        errorMessage = nil

        do {
            var results: [SearchResult] = []

            switch scope {
            case .all:
                // 搜索所有类型
                let songs = try await songRepository.searchSongs(query: query).async()
                let songLists = try await songListRepository.searchSongLists(query: query).async()
                let artists = try await songRepository.searchArtists(query: query).async()
                let albums = try await songRepository.searchAlbums(query: query).async()

                results.append(contentsOf: songs.map { SearchResult.song($0) })
                results.append(contentsOf: songLists.map { SearchResult.songList($0) })
                results.append(contentsOf: artists.map { SearchResult.artist($0) })
                results.append(contentsOf: albums.map { SearchResult.album($0) })

            case .songs:
                let songs = try await songRepository.searchSongs(query: query).async()
                results = songs.map { SearchResult.song($0) }

            case .playlists:
                let songLists = try await songListRepository.searchSongLists(query: query).async()
                results = songLists.map { SearchResult.songList($0) }

            case .artists:
                let artists = try await songRepository.searchArtists(query: query).async()
                results = artists.map { SearchResult.artist($0) }

            case .albums:
                let albums = try await songRepository.searchAlbums(query: query).async()
                results = albums.map { SearchResult.album($0) }
            }

            searchResults = results
        } catch {
            errorMessage = "搜索失败: \(error.localizedDescription)"
            searchResults = []
        }

        isSearching = false
    }
}

// MARK: - Search Scope
enum SearchScope: CaseIterable {
    case all
    case songs
    case playlists
    case artists
    case albums

    var displayName: String {
        switch self {
        case .all: return "全部"
        case .songs: return "歌曲"
        case .playlists: return "歌单"
        case .artists: return "艺术家"
        case .albums: return "专辑"
        }
    }
}

// MARK: - Search Result
enum SearchResult {
    case song(Song)
    case songList(SongList)
    case artist(String)
    case album(String)

    var id: String {
        switch self {
        case .song(let song):
            return "song_\(song.objectID)"
        case .songList(let songList):
            return "songList_\(songList.objectID)"
        case .artist(let artist):
            return "artist_\(artist)"
        case .album(let album):
            return "album_\(album)"
        }
    }
}

#Preview {
    SearchView()
        .environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
}
