import SwiftUI
import CoreData

struct AddSongsView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @Environment(\.dismiss) private var dismiss
    @StateObject private var viewModel = AddSongsViewModel()
    @State private var searchText = ""
    
    let songList: SongList
    
    var body: some View {
        NavigationView {
            VStack {
                // 搜索栏
                SearchBar(text: $searchText, placeholder: "搜索歌曲")
                    .padding(.horizontal)
                
                if viewModel.isLoading {
                    LoadingView(message: "加载歌曲中...")
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                } else if viewModel.filteredSongs.isEmpty {
                    EmptyStateView(
                        icon: "music.note",
                        title: searchText.isEmpty ? "没有可添加的歌曲" : "未找到匹配的歌曲",
                        subtitle: searchText.isEmpty ? "所有歌曲都已在歌单中" : "尝试使用其他关键词搜索"
                    )
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                } else {
                    // 歌曲列表
                    List {
                        ForEach(viewModel.filteredSongs) { song in
                            AddSongRow(
                                song: song,
                                isSelected: viewModel.selectedSongs.contains(song),
                                onToggle: {
                                    viewModel.toggleSongSelection(song)
                                }
                            )
                        }
                    }
                    .listStyle(PlainListStyle())
                }
                
                // 底部操作栏
                if !viewModel.selectedSongs.isEmpty {
                    VStack {
                        Divider()
                        
                        HStack {
                            Text("已选择 \(viewModel.selectedSongs.count) 首歌曲")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                            
                            Spacer()
                            
                            Button("添加到歌单") {
                                Task {
                                    await addSelectedSongs()
                                }
                            }
                            .buttonStyle(.borderedProminent)
                        }
                        .padding()
                    }
                    .background(Color(.systemBackground))
                }
            }
            .navigationTitle("添加歌曲")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("全选") {
                        selectAllSongs()
                    }
                    .disabled(viewModel.filteredSongs.isEmpty)
                }
            }
            .onAppear {
                viewModel.setContext(viewContext)
                Task {
                    await viewModel.loadAvailableSongs(excluding: songList)
                }
            }
            .onChange(of: searchText) { newValue in
                viewModel.searchSongs(query: newValue)
            }
        }
        .alert("错误", isPresented: .constant(viewModel.errorMessage != nil)) {
            Button("确定") {
                viewModel.errorMessage = nil
            }
        } message: {
            if let errorMessage = viewModel.errorMessage {
                Text(errorMessage)
            }
        }
    }
    
    private func addSelectedSongs() async {
        await viewModel.addSelectedSongs(to: songList)
        if viewModel.errorMessage == nil {
            dismiss()
        }
    }
    
    private func selectAllSongs() {
        for song in viewModel.filteredSongs {
            if !viewModel.selectedSongs.contains(song) {
                viewModel.toggleSongSelection(song)
            }
        }
    }
}

// MARK: - Add Song Row
struct AddSongRow: View {
    let song: Song
    let isSelected: Bool
    let onToggle: () -> Void
    
    var body: some View {
        HStack {
            // 选择指示器
            Button(action: onToggle) {
                Image(systemName: isSelected ? "checkmark.circle.fill" : "circle")
                    .foregroundColor(isSelected ? .accentColor : .secondary)
                    .font(.title3)
            }
            .buttonStyle(PlainButtonStyle())
            
            // 歌曲信息
            VStack(alignment: .leading, spacing: 4) {
                Text(song.displayTitle)
                    .font(.headline)
                    .foregroundColor(.primary)
                    .lineLimit(1)
                
                HStack {
                    Text(song.displayArtist)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .lineLimit(1)
                    
                    if !song.displayAlbum.isEmpty && song.displayAlbum != song.displayArtist {
                        Text("•")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Text(song.displayAlbum)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .lineLimit(1)
                    }
                }
            }
            
            Spacer()
            
            // 时长
            if song.duration > 0 {
                Text(song.formattedDuration)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding(.vertical, 4)
        .contentShape(Rectangle())
        .onTapGesture {
            onToggle()
        }
    }
}

// MARK: - Empty State View
struct EmptyStateView: View {
    let icon: String
    let title: String
    let subtitle: String
    
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: icon)
                .font(.system(size: 48))
                .foregroundColor(.secondary)
            
            VStack(spacing: 8) {
                Text(title)
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Text(subtitle)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
        }
        .padding()
    }
}

#Preview {
    let context = PersistenceController.preview.container.viewContext
    let songList = SongList(context: context, name: "测试歌单", type: .custom)
    
    return AddSongsView(songList: songList)
        .environment(\.managedObjectContext, context)
}
