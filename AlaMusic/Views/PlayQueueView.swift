import SwiftUI

struct PlayQueueView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var audioPlayerManager = AudioPlayerManager.shared
    @State private var editMode: EditMode = .inactive
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 当前播放歌曲
                if let currentSong = audioPlayerManager.currentSong {
                    CurrentPlayingSongView(song: currentSong)
                        .padding(.horizontal)
                        .padding(.vertical, 12)
                        .background(Color(.systemGray6))
                }
                
                // 播放队列
                if audioPlayerManager.playQueue.isEmpty {
                    EmptyQueueView()
                } else {
                    PlayQueueListView(
                        songs: audioPlayerManager.playQueue,
                        currentSong: audioPlayerManager.currentSong,
                        editMode: editMode,
                        onSongTap: { song in
                            if let index = audioPlayerManager.playQueue.firstIndex(of: song) {
                                audioPlayerManager.playAtIndex(index)
                            }
                        },
                        onSongRemove: { song in
                            audioPlayerManager.removeFromQueue(song)
                        },
                        onSongMove: { from, to in
                            audioPlayerManager.moveInQueue(from: from, to: to)
                        }
                    )
                }
            }
            .navigationTitle("播放队列")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("完成") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    HStack {
                        if !audioPlayerManager.playQueue.isEmpty {
                            Button(editMode == .active ? "完成" : "编辑") {
                                withAnimation {
                                    editMode = editMode == .active ? .inactive : .active
                                }
                            }
                        }
                        
                        Menu {
                            Button(action: {
                                audioPlayerManager.clearQueue()
                            }) {
                                Label("清空队列", systemImage: "trash")
                            }
                            .disabled(audioPlayerManager.playQueue.isEmpty)
                            
                            Button(action: {
                                audioPlayerManager.shuffleQueue()
                            }) {
                                Label("随机排序", systemImage: "shuffle")
                            }
                            .disabled(audioPlayerManager.playQueue.count <= 1)
                        } label: {
                            Image(systemName: "ellipsis.circle")
                        }
                    }
                }
            }
            .environment(\.editMode, $editMode)
        }
    }
}

// MARK: - Current Playing Song View
struct CurrentPlayingSongView: View {
    let song: Song
    @StateObject private var audioPlayerManager = AudioPlayerManager.shared
    
    var body: some View {
        HStack(spacing: 12) {
            // 专辑封面
            Group {
                if let artworkImage = song.albumArtworkImage {
                    Image(uiImage: artworkImage)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                } else {
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color.accentColor.opacity(0.3))
                        .overlay(
                            Image(systemName: "music.note")
                                .font(.title3)
                                .foregroundColor(.accentColor)
                        )
                }
            }
            .frame(width: 50, height: 50)
            .clipShape(RoundedRectangle(cornerRadius: 8))
            
            // 歌曲信息
            VStack(alignment: .leading, spacing: 4) {
                Text(song.title)
                    .font(.headline)
                    .foregroundColor(.primary)
                    .lineLimit(1)
                
                Text(song.displayArtist)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .lineLimit(1)
            }
            
            Spacer()
            
            // 播放状态指示器
            Image(systemName: audioPlayerManager.isPlaying ? "speaker.wave.2.fill" : "pause.fill")
                .font(.title3)
                .foregroundColor(.accentColor)
        }
    }
}

// MARK: - Play Queue List View
struct PlayQueueListView: View {
    let songs: [Song]
    let currentSong: Song?
    let editMode: EditMode
    let onSongTap: (Song) -> Void
    let onSongRemove: (Song) -> Void
    let onSongMove: (IndexSet, Int) -> Void
    
    var body: some View {
        List {
            ForEach(Array(songs.enumerated()), id: \.element.objectID) { index, song in
                PlayQueueSongRow(
                    song: song,
                    index: index + 1,
                    isCurrentSong: currentSong?.objectID == song.objectID,
                    editMode: editMode,
                    onTap: { onSongTap(song) }
                )
            }
            .onDelete { indexSet in
                for index in indexSet {
                    onSongRemove(songs[index])
                }
            }
            .onMove(perform: editMode == .active ? onSongMove : nil)
        }
        .listStyle(PlainListStyle())
    }
}

// MARK: - Play Queue Song Row
struct PlayQueueSongRow: View {
    let song: Song
    let index: Int
    let isCurrentSong: Bool
    let editMode: EditMode
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 12) {
                // 序号或播放状态
                if isCurrentSong {
                    Image(systemName: "speaker.wave.2.fill")
                        .font(.caption)
                        .foregroundColor(.accentColor)
                        .frame(width: 20)
                } else {
                    Text("\(index)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .frame(width: 20)
                }
                
                // 歌曲信息
                VStack(alignment: .leading, spacing: 4) {
                    Text(song.title)
                        .font(.body)
                        .foregroundColor(isCurrentSong ? .accentColor : .primary)
                        .lineLimit(1)
                    
                    HStack {
                        Text(song.displayArtist)
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        if !song.displayAlbum.isEmpty && song.displayAlbum != "未知专辑" {
                            Text("• \(song.displayAlbum)")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                    .lineLimit(1)
                }
                
                Spacer()
                
                // 时长
                if song.duration > 0 {
                    Text(formatDuration(song.duration))
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .monospacedDigit()
                }
                
                // 编辑模式下显示拖拽手柄
                if editMode == .active {
                    Image(systemName: "line.3.horizontal")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .padding(.vertical, 4)
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private func formatDuration(_ duration: TimeInterval) -> String {
        let minutes = Int(duration) / 60
        let seconds = Int(duration) % 60
        return String(format: "%d:%02d", minutes, seconds)
    }
}

// MARK: - Empty Queue View
struct EmptyQueueView: View {
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "list.bullet")
                .font(.system(size: 60))
                .foregroundColor(.secondary.opacity(0.5))
            
            Text("播放队列为空")
                .font(.title2)
                .foregroundColor(.secondary)
            
            Text("选择歌曲开始播放")
                .font(.subheadline)
                .foregroundColor(.secondary.opacity(0.8))
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding()
    }
}

// MARK: - Preview
struct PlayQueueView_Previews: PreviewProvider {
    static var previews: some View {
        PlayQueueView()
    }
}
