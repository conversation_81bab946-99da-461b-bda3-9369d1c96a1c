import SwiftUI

struct SettingView: View {
    @StateObject private var viewModel = SettingViewModel()
    @State private var showingOSSConfig = false
    @State private var showingCacheManagement = false
    @State private var showingAbout = false
    
    var body: some View {
        NavigationView {
            List {
                // 云存储设置
                Section("云存储") {
                    SettingRow(
                        icon: "icloud.fill",
                        title: "阿里云OSS配置",
                        subtitle: viewModel.ossConfigured ? "已配置" : "未配置",
                        iconColor: .blue
                    ) {
                        showingOSSConfig = true
                    }
                    
                    SettingRow(
                        icon: "arrow.clockwise.icloud.fill",
                        title: "同步设置",
                        subtitle: "自动同步云端文件",
                        iconColor: .green
                    ) {
                        // TODO: 实现同步设置
                    }
                }
                
                // 播放设置
                Section("播放") {
                    SettingRow(
                        icon: "speaker.wave.3.fill",
                        title: "音质设置",
                        subtitle: "高品质",
                        iconColor: .orange
                    ) {
                        // TODO: 实现音质设置
                    }
                    
                    ToggleSettingRow(
                        icon: "moon.fill",
                        title: "后台播放",
                        subtitle: "允许在后台继续播放",
                        iconColor: .purple,
                        isOn: $viewModel.backgroundPlayEnabled
                    )
                    
                    ToggleSettingRow(
                        icon: "airpods",
                        title: "自动播放",
                        subtitle: "连接耳机时自动播放",
                        iconColor: .gray,
                        isOn: $viewModel.autoPlayEnabled
                    )
                }
                
                // 缓存设置
                Section("缓存") {
                    SettingRow(
                        icon: "internaldrive.fill",
                        title: "缓存管理",
                        subtitle: viewModel.cacheInfo,
                        iconColor: .indigo
                    ) {
                        showingCacheManagement = true
                    }
                    
                    ToggleSettingRow(
                        icon: "wifi",
                        title: "仅WiFi下载",
                        subtitle: "只在WiFi环境下缓存歌曲",
                        iconColor: .blue,
                        isOn: $viewModel.wifiOnlyDownload
                    )
                    
                    SettingRow(
                        icon: "slider.horizontal.3",
                        title: "缓存大小限制",
                        subtitle: "\(viewModel.maxCacheSize)GB",
                        iconColor: .red
                    ) {
                        // TODO: 实现缓存大小设置
                    }
                }
                
                // 其他设置
                Section("其他") {
                    SettingRow(
                        icon: "info.circle.fill",
                        title: "关于",
                        subtitle: "版本 1.0.0",
                        iconColor: .gray
                    ) {
                        showingAbout = true
                    }
                    
                    SettingRow(
                        icon: "star.fill",
                        title: "评价应用",
                        subtitle: "在App Store中评价",
                        iconColor: .yellow
                    ) {
                        // TODO: 实现应用评价
                    }
                    
                    SettingRow(
                        icon: "questionmark.circle.fill",
                        title: "帮助与反馈",
                        subtitle: "获取帮助或提供反馈",
                        iconColor: .green
                    ) {
                        // TODO: 实现帮助页面
                    }
                }
            }
            .navigationTitle("设置")
            .navigationBarTitleDisplayMode(.large)
            .sheet(isPresented: $showingOSSConfig) {
                OSSConfigView()
            }
            .sheet(isPresented: $showingCacheManagement) {
                CacheManagementView()
            }
            .sheet(isPresented: $showingAbout) {
                AboutView()
            }
            .onAppear {
                viewModel.loadSettings()
            }
        }
    }
}

// MARK: - Setting Row
struct SettingRow: View {
    let icon: String
    let title: String
    let subtitle: String
    let iconColor: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 12) {
                Image(systemName: icon)
                    .font(.title3)
                    .foregroundColor(iconColor)
                    .frame(width: 28, height: 28)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.body)
                        .foregroundColor(.primary)
                    
                    Text(subtitle)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding(.vertical, 4)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Toggle Setting Row
struct ToggleSettingRow: View {
    let icon: String
    let title: String
    let subtitle: String
    let iconColor: Color
    @Binding var isOn: Bool
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(iconColor)
                .frame(width: 28, height: 28)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.body)
                    .foregroundColor(.primary)
                
                Text(subtitle)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            Toggle("", isOn: $isOn)
                .labelsHidden()
        }
        .padding(.vertical, 4)
    }
}

// MARK: - OSS Config View
struct OSSConfigView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var viewModel = OSSConfigViewModel()
    
    var body: some View {
        NavigationView {
            Form {
                Section {
                    TextField("Endpoint", text: $viewModel.endpoint)
                        .textContentType(.URL)
                        .autocapitalization(.none)
                        .disableAutocorrection(true)
                    
                    TextField("Bucket Name", text: $viewModel.bucketName)
                        .autocapitalization(.none)
                        .disableAutocorrection(true)
                    
                    TextField("Access Key ID", text: $viewModel.accessKeyId)
                        .autocapitalization(.none)
                        .disableAutocorrection(true)
                    
                    SecureField("Access Key Secret", text: $viewModel.accessKeySecret)
                } header: {
                    Text("阿里云OSS配置")
                } footer: {
                    Text("请输入您的阿里云OSS配置信息。这些信息将安全地存储在本地。")
                }
                
                Section {
                    Button("测试连接") {
                        Task {
                            await viewModel.testConnection()
                        }
                    }
                    .disabled(viewModel.isTesting || !viewModel.isFormValid)
                    
                    if viewModel.isTesting {
                        HStack {
                            ProgressView()
                                .scaleEffect(0.8)
                            Text("测试连接中...")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                    
                    if let testResult = viewModel.testResult {
                        HStack {
                            Image(systemName: testResult.success ? "checkmark.circle.fill" : "xmark.circle.fill")
                                .foregroundColor(testResult.success ? .green : .red)
                            Text(testResult.message)
                                .font(.caption)
                                .foregroundColor(testResult.success ? .green : .red)
                        }
                    }
                }
            }
            .navigationTitle("OSS配置")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("保存") {
                        Task {
                            await viewModel.saveConfiguration()
                            dismiss()
                        }
                    }
                    .disabled(!viewModel.isFormValid || viewModel.isSaving)
                }
            }
            .onAppear {
                viewModel.loadCurrentConfiguration()
            }
        }
    }
}

// MARK: - Cache Management View
struct CacheManagementView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var cacheManager = CacheManager.shared
    @State private var showingClearConfirmation = false
    
    var body: some View {
        NavigationView {
            List {
                Section {
                    HStack {
                        Text("已使用空间")
                        Spacer()
                        Text(ByteCountFormatter.string(fromByteCount: cacheManager.totalCacheSize, countStyle: .file))
                            .foregroundColor(.secondary)
                    }
                    
                    HStack {
                        Text("可用空间")
                        Spacer()
                        Text(ByteCountFormatter.string(fromByteCount: cacheManager.availableSpace, countStyle: .file))
                            .foregroundColor(.secondary)
                    }
                } header: {
                    Text("存储信息")
                }
                
                Section {
                    Button("清理缓存") {
                        showingClearConfirmation = true
                    }
                    .foregroundColor(.red)
                    
                    Button("清理过期缓存") {
                        Task {
                            _ = try? await cacheManager.cleanupOldCache().async()
                        }
                    }
                } header: {
                    Text("缓存管理")
                } footer: {
                    Text("清理缓存将删除所有本地缓存的音频文件，但不会影响云端文件。")
                }
            }
            .navigationTitle("缓存管理")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        dismiss()
                    }
                }
            }
            .confirmationDialog("确认清理", isPresented: $showingClearConfirmation) {
                Button("清理所有缓存", role: .destructive) {
                    Task {
                        _ = try? await cacheManager.clearCache().async()
                    }
                }
                Button("取消", role: .cancel) { }
            } message: {
                Text("这将删除所有本地缓存的音频文件，确定要继续吗？")
            }
        }
    }
}

// MARK: - About View
struct AboutView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack(spacing: 24) {
                // 应用图标
                Image(systemName: "music.note")
                    .font(.system(size: 80))
                    .foregroundColor(.accentColor)
                    .padding(.top, 40)
                
                // 应用信息
                VStack(spacing: 8) {
                    Text("阿拉摩音乐")
                        .font(.title)
                        .fontWeight(.bold)
                    
                    Text("版本 1.0.0")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                // 描述
                Text("一款基于阿里云OSS的云端音乐播放器，支持在线播放和本地缓存。")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 32)
                
                Spacer()
                
                // 版权信息
                VStack(spacing: 4) {
                    Text("© 2024 阿拉摩音乐")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text("使用SwiftUI构建")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .padding(.bottom, 40)
            }
            .navigationTitle("关于")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        dismiss()
                    }
                }
            }
        }
    }
}

// MARK: - Preview
struct SettingView_Previews: PreviewProvider {
    static var previews: some View {
        SettingView()
    }
}
