import SwiftUI

struct PlayerView: View {
    @StateObject private var audioPlayerManager = AudioPlayerManager.shared
    @State private var showingPlayQueue = false
    @State private var isDraggingSlider = false
    @State private var sliderValue: Double = 0
    
    var body: some View {
        GeometryReader { geometry in
            VStack(spacing: 0) {
                if let currentSong = audioPlayerManager.currentSong {
                    // 主播放界面
                    ScrollView {
                        VStack(spacing: 24) {
                            // 专辑封面
                            AlbumArtworkView(song: currentSong)
                                .frame(width: min(geometry.size.width - 80, 300),
                                       height: min(geometry.size.width - 80, 300))
                            
                            // 歌曲信息
                            SongInfoView(song: currentSong)
                                .padding(.horizontal, 20)
                            
                            // 进度条
                            ProgressSliderView(
                                currentTime: isDraggingSlider ? sliderValue : audioPlayerManager.currentTime,
                                duration: audioPlayerManager.duration,
                                isDragging: $isDraggingSlider,
                                sliderValue: $sliderValue,
                                onSeek: { time in
                                    audioPlayerManager.seek(to: time)
                                }
                            )
                            .padding(.horizontal, 20)
                            
                            // 播放控制
                            PlaybackControlsView()
                                .padding(.horizontal, 20)
                            
                            // 音量控制
                            VolumeControlView()
                                .padding(.horizontal, 20)
                                .padding(.bottom, 20)
                        }
                    }
                } else {
                    // 空状态
                    EmptyPlayerView()
                }
            }
        }
        .background(Color(.systemBackground))
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button(action: { showingPlayQueue = true }) {
                    Image(systemName: "list.bullet")
                }
                .disabled(audioPlayerManager.playQueue.isEmpty)
            }
        }
        .sheet(isPresented: $showingPlayQueue) {
            PlayQueueView()
        }
    }
}

// MARK: - Album Artwork View
struct AlbumArtworkView: View {
    let song: Song
    
    var body: some View {
        Group {
            if let artworkImage = song.albumArtworkImage {
                Image(uiImage: artworkImage)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
            } else {
                RoundedRectangle(cornerRadius: 16)
                    .fill(
                        LinearGradient(
                            colors: [Color.accentColor.opacity(0.3), Color.accentColor.opacity(0.1)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .overlay(
                        Image(systemName: "music.note")
                            .font(.system(size: 60))
                            .foregroundColor(.accentColor.opacity(0.6))
                    )
            }
        }
        .clipShape(RoundedRectangle(cornerRadius: 16))
        .shadow(color: .black.opacity(0.2), radius: 10, x: 0, y: 5)
    }
}

// MARK: - Song Info View
struct SongInfoView: View {
    let song: Song
    
    var body: some View {
        VStack(spacing: 8) {
            Text(song.title)
                .font(.title2)
                .fontWeight(.semibold)
                .foregroundColor(.primary)
                .multilineTextAlignment(.center)
                .lineLimit(2)
            
            Text(song.displayArtist)
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .lineLimit(1)
            
            if !song.displayAlbum.isEmpty && song.displayAlbum != "未知专辑" {
                Text(song.displayAlbum)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .lineLimit(1)
            }
        }
    }
}

// MARK: - Progress Slider View
struct ProgressSliderView: View {
    let currentTime: TimeInterval
    let duration: TimeInterval
    @Binding var isDragging: Bool
    @Binding var sliderValue: Double
    let onSeek: (TimeInterval) -> Void
    
    var body: some View {
        VStack(spacing: 8) {
            // 进度条
            Slider(
                value: Binding(
                    get: { isDragging ? sliderValue : currentTime },
                    set: { newValue in
                        sliderValue = newValue
                        if !isDragging {
                            onSeek(newValue)
                        }
                    }
                ),
                in: 0...max(duration, 1),
                onEditingChanged: { editing in
                    isDragging = editing
                    if !editing {
                        onSeek(sliderValue)
                    }
                }
            )
            .accentColor(.accentColor)
            
            // 时间标签
            HStack {
                Text(formatTime(isDragging ? sliderValue : currentTime))
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .monospacedDigit()
                
                Spacer()
                
                Text(formatTime(duration))
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .monospacedDigit()
            }
        }
    }
    
    private func formatTime(_ time: TimeInterval) -> String {
        let minutes = Int(time) / 60
        let seconds = Int(time) % 60
        return String(format: "%d:%02d", minutes, seconds)
    }
}

// MARK: - Playback Controls View
struct PlaybackControlsView: View {
    @StateObject private var audioPlayerManager = AudioPlayerManager.shared
    
    var body: some View {
        HStack(spacing: 40) {
            // 播放模式
            Button(action: {
                let modes: [PlayMode] = [.sequential, .shuffle, .repeatOne, .repeatAll]
                if let currentIndex = modes.firstIndex(of: audioPlayerManager.playMode) {
                    let nextIndex = (currentIndex + 1) % modes.count
                    audioPlayerManager.setPlayMode(modes[nextIndex])
                }
            }) {
                Image(systemName: audioPlayerManager.playMode.iconName)
                    .font(.title3)
                    .foregroundColor(.accentColor)
            }
            
            // 上一首
            Button(action: {
                audioPlayerManager.previous()
            }) {
                Image(systemName: "backward.fill")
                    .font(.title2)
                    .foregroundColor(.primary)
            }
            .disabled(audioPlayerManager.playQueue.isEmpty)
            
            // 播放/暂停
            Button(action: {
                if audioPlayerManager.isPlaying {
                    audioPlayerManager.pause()
                } else {
                    audioPlayerManager.resume()
                }
            }) {
                Image(systemName: audioPlayerManager.isPlaying ? "pause.circle.fill" : "play.circle.fill")
                    .font(.system(size: 64))
                    .foregroundColor(.accentColor)
            }
            .disabled(audioPlayerManager.currentSong == nil)
            
            // 下一首
            Button(action: {
                audioPlayerManager.next()
            }) {
                Image(systemName: "forward.fill")
                    .font(.title2)
                    .foregroundColor(.primary)
            }
            .disabled(audioPlayerManager.playQueue.isEmpty)
            
            // 收藏
            Button(action: {
                if let currentSong = audioPlayerManager.currentSong {
                    // TODO: 实现收藏功能
                    print("Toggle favorite for: \(currentSong.title)")
                }
            }) {
                Image(systemName: audioPlayerManager.currentSong?.isFavorite == true ? "heart.fill" : "heart")
                    .font(.title3)
                    .foregroundColor(audioPlayerManager.currentSong?.isFavorite == true ? .red : .primary)
            }
            .disabled(audioPlayerManager.currentSong == nil)
        }
    }
}

// MARK: - Volume Control View
struct VolumeControlView: View {
    @StateObject private var audioPlayerManager = AudioPlayerManager.shared
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: "speaker.fill")
                .font(.caption)
                .foregroundColor(.secondary)
            
            Slider(
                value: Binding(
                    get: { audioPlayerManager.volume },
                    set: { audioPlayerManager.setVolume($0) }
                ),
                in: 0...1
            )
            .accentColor(.accentColor)
            
            Image(systemName: "speaker.wave.3.fill")
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
}

// MARK: - Empty Player View
struct EmptyPlayerView: View {
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "music.note")
                .font(.system(size: 80))
                .foregroundColor(.secondary.opacity(0.5))
            
            Text("暂无播放内容")
                .font(.title2)
                .foregroundColor(.secondary)
            
            Text("选择一首歌曲开始播放")
                .font(.subheadline)
                .foregroundColor(.secondary.opacity(0.8))
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

// MARK: - Player Tab View (for TabView)
struct PlayerTabView: View {
    @StateObject private var audioPlayerManager = AudioPlayerManager.shared
    
    var body: some View {
        NavigationView {
            if audioPlayerManager.currentSong != nil {
                PlayerView()
                    .navigationTitle("正在播放")
            } else {
                EmptyPlayerView()
                    .navigationTitle("播放")
            }
        }
    }
}

// MARK: - Preview
struct PlayerView_Previews: PreviewProvider {
    static var previews: some View {
        PlayerView()
    }
}
