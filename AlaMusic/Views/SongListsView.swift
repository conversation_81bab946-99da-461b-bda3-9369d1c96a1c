import SwiftUI
import CoreData

struct SongListsView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @StateObject private var viewModel = SongListsViewModel()
    @State private var showingCreatePlaylist = false
    @State private var searchText = ""
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 搜索栏
                SearchBar(text: $searchText, placeholder: "搜索歌单")
                    .padding(.horizontal)
                    .padding(.top, 8)
                
                // 歌单列表
                List {
                    // 系统歌单部分
                    Section("系统歌单") {
                        ForEach(viewModel.systemSongLists) { songList in
                            NavigationLink(destination: SongListDetailView(songList: songList)) {
                                SystemSongListRow(songList: songList)
                            }
                        }
                    }
                    
                    // 自定义歌单部分
                    Section {
                        ForEach(viewModel.customSongLists) { songList in
                            NavigationLink(destination: SongListDetailView(songList: songList)) {
                                CustomSongListRow(songList: songList)
                            }
                        }
                        .onDelete(perform: deleteCustomSongLists)
                    } header: {
                        HStack {
                            Text("我的歌单")
                            Spacer()
                            Button(action: { showingCreatePlaylist = true }) {
                                Image(systemName: "plus.circle.fill")
                                    .foregroundColor(.accentColor)
                            }
                        }
                    }
                }
                .listStyle(InsetGroupedListStyle())
                .searchable(text: $searchText, prompt: "搜索歌单")
                .refreshable {
                    await viewModel.refresh()
                }
            }
            .navigationTitle("歌单")
            .navigationBarTitleDisplayMode(.large)
            .sheet(isPresented: $showingCreatePlaylist) {
                CreatePlaylistView()
            }
            .onAppear {
                viewModel.setContext(viewContext)
                Task {
                    await viewModel.loadSongLists()
                }
            }
            .onChange(of: searchText) { newValue in
                viewModel.searchSongLists(query: newValue)
            }
        }
    }
    
    private func deleteCustomSongLists(offsets: IndexSet) {
        Task {
            await viewModel.deleteCustomSongLists(at: offsets)
        }
    }
}

// MARK: - System Song List Row
struct SystemSongListRow: View {
    let songList: SongList
    
    var body: some View {
        HStack(spacing: 12) {
            // 图标
            Image(systemName: songList.displayIcon)
                .font(.title2)
                .foregroundColor(.accentColor)
                .frame(width: 40, height: 40)
                .background(Color.accentColor.opacity(0.1))
                .clipShape(RoundedRectangle(cornerRadius: 8))
            
            // 信息
            VStack(alignment: .leading, spacing: 4) {
                Text(songList.displayName)
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Text("\(songList.songCount) 首歌曲")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            // 箭头
            Image(systemName: "chevron.right")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding(.vertical, 4)
    }
}

// MARK: - Custom Song List Row
struct CustomSongListRow: View {
    let songList: SongList
    
    var body: some View {
        HStack(spacing: 12) {
            // 封面
            AsyncImage(url: nil) { image in
                image
                    .resizable()
                    .aspectRatio(contentMode: .fill)
            } placeholder: {
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color.gray.opacity(0.3))
                    .overlay(
                        Image(systemName: "music.note.list")
                            .font(.title3)
                            .foregroundColor(.secondary)
                    )
            }
            .frame(width: 50, height: 50)
            .clipShape(RoundedRectangle(cornerRadius: 8))
            
            // 信息
            VStack(alignment: .leading, spacing: 4) {
                Text(songList.name)
                    .font(.headline)
                    .foregroundColor(.primary)
                    .lineLimit(1)
                
                HStack {
                    Text("\(songList.songCount) 首歌曲")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    if let updatedAt = songList.updatedAt {
                        Text("• \(updatedAt, style: .relative)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
            
            Spacer()
            
            // 箭头
            Image(systemName: "chevron.right")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding(.vertical, 4)
    }
}

// MARK: - Search Bar
struct SearchBar: View {
    @Binding var text: String
    let placeholder: String
    
    var body: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.secondary)
            
            TextField(placeholder, text: $text)
                .textFieldStyle(PlainTextFieldStyle())
            
            if !text.isEmpty {
                Button(action: { text = "" }) {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(Color(.systemGray6))
        .clipShape(RoundedRectangle(cornerRadius: 10))
    }
}

// MARK: - Create Playlist View
struct CreatePlaylistView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @Environment(\.dismiss) private var dismiss
    @State private var playlistName = ""
    @State private var isCreating = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // 封面选择区域
                VStack {
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.gray.opacity(0.3))
                        .frame(width: 120, height: 120)
                        .overlay(
                            VStack {
                                Image(systemName: "photo")
                                    .font(.title)
                                    .foregroundColor(.secondary)
                                Text("添加封面")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        )
                        .onTapGesture {
                            // TODO: 实现封面选择
                        }
                }
                .padding(.top, 20)
                
                // 歌单名称输入
                VStack(alignment: .leading, spacing: 8) {
                    Text("歌单名称")
                        .font(.headline)
                        .foregroundColor(.primary)
                    
                    TextField("请输入歌单名称", text: $playlistName)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                }
                .padding(.horizontal)
                
                Spacer()
            }
            .navigationTitle("创建歌单")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("创建") {
                        createPlaylist()
                    }
                    .disabled(playlistName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty || isCreating)
                }
            }
        }
    }
    
    private func createPlaylist() {
        guard !playlistName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else { return }
        
        isCreating = true
        
        let songList = SongList(context: viewContext,
                               name: playlistName.trimmingCharacters(in: .whitespacesAndNewlines),
                               type: .custom)
        
        do {
            try viewContext.save()
            dismiss()
        } catch {
            print("Failed to create playlist: \(error)")
        }
        
        isCreating = false
    }
}

// MARK: - Preview
struct SongListsView_Previews: PreviewProvider {
    static var previews: some View {
        SongListsView()
            .environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
    }
}
