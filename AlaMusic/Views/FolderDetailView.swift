import SwiftUI
import CoreData

struct FolderDetailView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @StateObject private var viewModel = FolderDetailViewModel()
    @State private var searchText = ""
    @State private var showingBatchActions = false
    @State private var selectedFiles: Set<CloudFile> = []
    
    let folder: CloudFolder
    
    var body: some View {
        VStack {
            if viewModel.isLoading {
                LoadingView(message: "加载文件中...")
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else if viewModel.files.isEmpty {
                EmptyFolderView()
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else {
                List {
                    ForEach(viewModel.filteredFiles) { file in
                        CloudFileRow(
                            file: file,
                            isSelected: selectedFiles.contains(file),
                            showingBatchActions: showingBatchActions,
                            onToggleSelection: {
                                toggleFileSelection(file)
                            },
                            onDownload: {
                                Task {
                                    await viewModel.downloadFile(file)
                                }
                            },
                            onPlay: {
                                Task {
                                    await playFile(file)
                                }
                            }
                        )
                    }
                }
                .listStyle(PlainListStyle())
                .searchable(text: $searchText, prompt: "搜索文件")
                .refreshable {
                    await viewModel.refresh()
                }
                
                // 批量操作栏
                if showingBatchActions && !selectedFiles.isEmpty {
                    BatchActionsBar(
                        selectedCount: selectedFiles.count,
                        onDownloadAll: {
                            Task {
                                await downloadSelectedFiles()
                            }
                        },
                        onAddToPlaylist: {
                            // TODO: 实现添加到歌单功能
                        },
                        onCancel: {
                            selectedFiles.removeAll()
                            showingBatchActions = false
                        }
                    )
                }
            }
        }
        .navigationTitle(folder.displayName)
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button(showingBatchActions ? "取消" : "选择") {
                    withAnimation {
                        showingBatchActions.toggle()
                        if !showingBatchActions {
                            selectedFiles.removeAll()
                        }
                    }
                }
                .disabled(viewModel.files.isEmpty)
            }
        }
        .onAppear {
            viewModel.setContext(viewContext)
            Task {
                await viewModel.loadFiles(in: folder)
            }
        }
        .onChange(of: searchText) { newValue in
            viewModel.searchFiles(query: newValue)
        }
        .alert("错误", isPresented: .constant(viewModel.errorMessage != nil)) {
            Button("确定") {
                viewModel.errorMessage = nil
            }
        } message: {
            if let errorMessage = viewModel.errorMessage {
                Text(errorMessage)
            }
        }
    }
    
    private func toggleFileSelection(_ file: CloudFile) {
        if selectedFiles.contains(file) {
            selectedFiles.remove(file)
        } else {
            selectedFiles.insert(file)
        }
    }
    
    private func downloadSelectedFiles() async {
        for file in selectedFiles {
            await viewModel.downloadFile(file)
        }
        selectedFiles.removeAll()
        showingBatchActions = false
    }
    
    private func playFile(_ file: CloudFile) async {
        // 首先下载文件（如果需要）
        await viewModel.downloadFile(file)
        
        // 然后播放文件
        if let song = viewModel.getSong(for: file) {
            let audioPlayerManager = AudioPlayerManager.shared
            audioPlayerManager.setPlayQueue([song])
            audioPlayerManager.playAtIndex(0)
        }
    }
}

// MARK: - Cloud File Row
struct CloudFileRow: View {
    let file: CloudFile
    let isSelected: Bool
    let showingBatchActions: Bool
    let onToggleSelection: () -> Void
    let onDownload: () -> Void
    let onPlay: () -> Void
    
    var body: some View {
        HStack(spacing: 12) {
            // 选择指示器（批量操作模式）
            if showingBatchActions {
                Button(action: onToggleSelection) {
                    Image(systemName: isSelected ? "checkmark.circle.fill" : "circle")
                        .foregroundColor(isSelected ? .accentColor : .secondary)
                        .font(.title3)
                }
                .buttonStyle(PlainButtonStyle())
            }
            
            // 文件图标
            Image(systemName: file.isAudioFile ? "music.note" : "doc")
                .font(.title3)
                .foregroundColor(file.isAudioFile ? .accentColor : .secondary)
                .frame(width: 30)
            
            // 文件信息
            VStack(alignment: .leading, spacing: 4) {
                Text(file.displayName)
                    .font(.subheadline)
                    .foregroundColor(.primary)
                    .lineLimit(1)
                
                HStack {
                    Text(file.formattedSize)
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    if let modifiedDate = file.modifiedDate {
                        Text("•")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Text(modifiedDate, style: .relative)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    if file.isDownloaded {
                        Text("•")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Text("已下载")
                            .font(.caption)
                            .foregroundColor(.green)
                    }
                }
            }
            
            Spacer()
            
            // 操作按钮
            if !showingBatchActions {
                HStack(spacing: 8) {
                    if file.isAudioFile {
                        Button(action: onPlay) {
                            Image(systemName: "play.fill")
                                .font(.caption)
                                .foregroundColor(.accentColor)
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                    
                    if !file.isDownloaded {
                        Button(action: onDownload) {
                            Image(systemName: "icloud.and.arrow.down")
                                .font(.caption)
                                .foregroundColor(.accentColor)
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                }
            }
        }
        .padding(.vertical, 4)
        .contentShape(Rectangle())
        .onTapGesture {
            if showingBatchActions {
                onToggleSelection()
            } else if file.isAudioFile {
                onPlay()
            }
        }
    }
}

// MARK: - Batch Actions Bar
struct BatchActionsBar: View {
    let selectedCount: Int
    let onDownloadAll: () -> Void
    let onAddToPlaylist: () -> Void
    let onCancel: () -> Void
    
    var body: some View {
        VStack {
            Divider()
            
            HStack {
                Text("已选择 \(selectedCount) 个文件")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                Button("下载全部", action: onDownloadAll)
                    .buttonStyle(.bordered)
                
                Button("添加到歌单", action: onAddToPlaylist)
                    .buttonStyle(.borderedProminent)
                
                Button("取消", action: onCancel)
                    .buttonStyle(.bordered)
            }
            .padding()
        }
        .background(Color(.systemBackground))
    }
}

// MARK: - Empty Folder View
struct EmptyFolderView: View {
    var body: some View {
        VStack(spacing: 24) {
            Image(systemName: "folder")
                .font(.system(size: 60))
                .foregroundColor(.secondary)
            
            VStack(spacing: 8) {
                Text("文件夹为空")
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
                
                Text("此文件夹中没有文件")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
        }
        .padding()
    }
}

// MARK: - Folder Detail ViewModel
@MainActor
class FolderDetailViewModel: ObservableObject {
    @Published var files: [CloudFile] = []
    @Published var filteredFiles: [CloudFile] = []
    @Published var isLoading = false
    @Published var errorMessage: String?

    private var context: NSManagedObjectContext?
    private var cloudStorageManager: CloudStorageManager?
    private var songRepository: SongRepository?

    func setContext(_ context: NSManagedObjectContext) {
        self.context = context
        self.cloudStorageManager = CloudStorageManager.shared
        self.songRepository = SongRepository(context: context)
    }

    func loadFiles(in folder: CloudFolder) async {
        guard let cloudStorageManager = cloudStorageManager else { return }

        isLoading = true
        errorMessage = nil

        do {
            let loadedFiles = try await cloudStorageManager.listFiles(in: folder.path)
            files = loadedFiles
            filteredFiles = loadedFiles
        } catch {
            errorMessage = "加载文件失败: \(error.localizedDescription)"
            files = []
            filteredFiles = []
        }

        isLoading = false
    }

    func refresh() async {
        // 重新加载当前文件夹
        // 这里需要保存当前文件夹引用
    }

    func searchFiles(query: String) {
        let trimmedQuery = query.trimmingCharacters(in: .whitespacesAndNewlines).lowercased()

        if trimmedQuery.isEmpty {
            filteredFiles = files
        } else {
            filteredFiles = files.filter { file in
                file.name.lowercased().contains(trimmedQuery)
            }
        }
    }

    func downloadFile(_ file: CloudFile) async {
        guard let cloudStorageManager = cloudStorageManager else { return }

        do {
            try await cloudStorageManager.downloadFile(file)
            // 更新文件状态
            if let index = files.firstIndex(where: { $0.id == file.id }) {
                files[index] = CloudFile(
                    name: file.name,
                    path: file.path,
                    size: file.size,
                    modifiedDate: file.modifiedDate,
                    isDownloaded: true
                )
            }
        } catch {
            errorMessage = "下载文件失败: \(error.localizedDescription)"
        }
    }

    func getSong(for file: CloudFile) -> Song? {
        guard let songRepository = songRepository else { return nil }

        // 根据文件路径查找对应的歌曲
        // 这里需要实现文件路径到歌曲的映射
        return nil
    }
}

// MARK: - Cloud File Model
struct CloudFile: Identifiable, Hashable {
    let id = UUID()
    let name: String
    let path: String
    let size: Int64
    let modifiedDate: Date?
    let isDownloaded: Bool

    init(name: String, path: String, size: Int64, modifiedDate: Date?, isDownloaded: Bool = false) {
        self.name = name
        self.path = path
        self.size = size
        self.modifiedDate = modifiedDate
        self.isDownloaded = isDownloaded
    }

    var displayName: String {
        return name
    }

    var isAudioFile: Bool {
        let audioExtensions = ["mp3", "m4a", "wav", "flac", "aac", "ogg"]
        let fileExtension = (name as NSString).pathExtension.lowercased()
        return audioExtensions.contains(fileExtension)
    }

    var formattedSize: String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useKB, .useMB, .useGB]
        formatter.countStyle = .file
        return formatter.string(fromByteCount: size)
    }
}

#Preview {
    let folder = CloudFolder(name: "测试文件夹", path: "/test", fileCount: 5, modifiedDate: Date())

    return FolderDetailView(folder: folder)
        .environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
}
