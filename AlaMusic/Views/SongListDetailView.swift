import SwiftUI
import CoreData

struct SongListDetailView: View {
    let songList: SongList
    @Environment(\.managedObjectContext) private var viewContext
    @StateObject private var viewModel = SongListDetailViewModel()
    @State private var showingAddSongs = false
    @State private var searchText = ""
    
    var body: some View {
        VStack(spacing: 0) {
            // 搜索栏
            if !viewModel.songs.isEmpty {
                SearchBar(text: $searchText, placeholder: "搜索歌曲")
                    .padding(.horizontal)
                    .padding(.top, 8)
            }
            
            // 歌曲列表
            if viewModel.isLoading {
                LoadingView()
            } else if viewModel.filteredSongs.isEmpty {
                EmptySongListView(
                    songList: songList,
                    onAddSongs: {
                        if songList.type == .custom {
                            showingAddSongs = true
                        }
                    }
                )
            } else {
                SongListContentView(
                    songs: viewModel.filteredSongs,
                    songList: songList,
                    onSongTap: { song in
                        playFromSongList(song: song)
                    },
                    onSongRemove: { song in
                        Task {
                            await viewModel.removeSong(song)
                        }
                    }
                )
            }
        }
        .navigationTitle(songList.displayName)
        .navigationBarTitleDisplayMode(.large)
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Menu {
                    if songList.songListType == .custom {
                        Button(action: { showingAddSongs = true }) {
                            Label("添加歌曲", systemImage: "plus")
                        }
                    }
                    
                    Button(action: {
                        viewModel.playAllSongs()
                    }) {
                        Label("播放全部", systemImage: "play.fill")
                    }
                    .disabled(viewModel.songs.isEmpty)

                    Button(action: {
                        viewModel.shufflePlay()
                    }) {
                        Label("随机播放", systemImage: "shuffle")
                    }
                    .disabled(viewModel.songs.isEmpty)
                } label: {
                    Image(systemName: "ellipsis.circle")
                }
            }
        }
        .refreshable {
            await viewModel.refresh()
        }
        .searchable(text: $searchText, prompt: "搜索歌曲")
        .onChange(of: searchText) { newValue in
            viewModel.searchSongs(query: newValue)
        }
        .sheet(isPresented: $showingAddSongs) {
            AddSongsView(songList: songList)
        }
        .onAppear {
            viewModel.setContext(viewContext)
            viewModel.setSongList(songList)
        }
    }
    
    private func playFromSongList(song: Song) {
        let audioPlayerManager = AudioPlayerManager.shared
        audioPlayerManager.play(songs: viewModel.songs, startIndex: viewModel.songs.firstIndex(of: song) ?? 0)
    }
    

}

// MARK: - Song List Content View
struct SongListContentView: View {
    let songs: [Song]
    let songList: SongList
    let onSongTap: (Song) -> Void
    let onSongRemove: (Song) -> Void
    
    var body: some View {
        List {
            ForEach(songs, id: \.objectID) { song in
                SongRowView(
                    song: song,
                    showArtist: true,
                    onTap: { onSongTap(song) }
                )
                .swipeActions(edge: .trailing, allowsFullSwipe: false) {
                    if songList.type == .custom {
                        Button("移除", role: .destructive) {
                            onSongRemove(song)
                        }
                    }
                    
                    Button("收藏") {
                        // TODO: 实现收藏功能
                    }
                    .tint(.yellow)
                }
            }
        }
        .listStyle(PlainListStyle())
    }
}

// MARK: - Song Row View
struct SongRowView: View {
    let song: Song
    let showArtist: Bool
    let onTap: () -> Void
    @StateObject private var audioPlayerManager = AudioPlayerManager.shared
    
    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 12) {
                // 播放状态指示器
                if audioPlayerManager.currentSong?.objectID == song.objectID {
                    Image(systemName: audioPlayerManager.isPlaying ? "speaker.wave.2.fill" : "pause.fill")
                        .font(.caption)
                        .foregroundColor(.accentColor)
                        .frame(width: 20)
                } else {
                    Text("\(songs.firstIndex(of: song) ?? 0 + 1)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .frame(width: 20)
                }
                
                // 歌曲信息
                VStack(alignment: .leading, spacing: 4) {
                    Text(song.title)
                        .font(.body)
                        .foregroundColor(.primary)
                        .lineLimit(1)
                    
                    if showArtist {
                        HStack {
                            Text(song.displayArtist)
                                .font(.caption)
                                .foregroundColor(.secondary)
                            
                            if !song.displayAlbum.isEmpty && song.displayAlbum != "未知专辑" {
                                Text("• \(song.displayAlbum)")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                        .lineLimit(1)
                    }
                }
                
                Spacer()
                
                // 时长
                if song.duration > 0 {
                    Text(formatDuration(song.duration))
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .monospacedDigit()
                }
                
                // 更多操作
                Button(action: {
                    // TODO: 显示歌曲操作菜单
                }) {
                    Image(systemName: "ellipsis")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .padding(.vertical, 4)
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private func formatDuration(_ duration: TimeInterval) -> String {
        let minutes = Int(duration) / 60
        let seconds = Int(duration) % 60
        return String(format: "%d:%02d", minutes, seconds)
    }
}

// MARK: - Empty Song List View
struct EmptySongListView: View {
    let songList: SongList
    let onAddSongs: () -> Void
    
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: songList.type == .system ? "music.note.list" : "plus.circle")
                .font(.system(size: 80))
                .foregroundColor(.secondary.opacity(0.5))
            
            Text(songList.type == .system ? "暂无歌曲" : "歌单为空")
                .font(.title2)
                .foregroundColor(.secondary)
            
            Text(songList.type == .system ? 
                 "当前没有符合条件的歌曲" : 
                 "点击下方按钮添加歌曲到歌单")
                .font(.subheadline)
                .foregroundColor(.secondary.opacity(0.8))
                .multilineTextAlignment(.center)
            
            if songList.type == .custom {
                Button("添加歌曲") {
                    onAddSongs()
                }
                .buttonStyle(.borderedProminent)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding()
    }
}

// MARK: - Add Songs to Playlist View
struct AddSongsToPlaylistView: View {
    let songList: SongList
    @Environment(\.dismiss) private var dismiss
    @StateObject private var viewModel = AddSongsViewModel()
    @State private var searchText = ""
    
    var body: some View {
        NavigationView {
            VStack {
                // 搜索栏
                SearchBar(text: $searchText, placeholder: "搜索歌曲")
                    .padding(.horizontal)
                    .padding(.top, 8)
                
                // 歌曲列表
                List {
                    ForEach(viewModel.filteredSongs, id: \.objectID) { song in
                        HStack {
                            Button(action: {
                                viewModel.toggleSongSelection(song)
                            }) {
                                Image(systemName: viewModel.selectedSongs.contains(song) ? "checkmark.circle.fill" : "circle")
                                    .foregroundColor(viewModel.selectedSongs.contains(song) ? .accentColor : .secondary)
                            }
                            
                            SongRowView(song: song, showArtist: true) {
                                viewModel.toggleSongSelection(song)
                            }
                        }
                    }
                }
                .listStyle(PlainListStyle())
            }
            .navigationTitle("添加歌曲")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("添加") {
                        Task {
                            await viewModel.addSelectedSongs(to: songList)
                            dismiss()
                        }
                    }
                    .disabled(viewModel.selectedSongs.isEmpty)
                }
            }
            .searchable(text: $searchText, prompt: "搜索歌曲")
            .onChange(of: searchText) { newValue in
                viewModel.searchSongs(query: newValue)
            }
            .onAppear {
                Task {
                    await viewModel.loadAvailableSongs(excluding: songList)
                }
            }
        }
    }
}

// MARK: - Preview
struct SongListDetailView_Previews: PreviewProvider {
    static var previews: some View {
        let context = PersistenceController.preview.container.viewContext
        let songList = SongList(context: context, name: "我的歌单", type: .custom)
        
        return NavigationView {
            SongListDetailView(songList: songList)
        }
        .environment(\.managedObjectContext, context)
    }
}
