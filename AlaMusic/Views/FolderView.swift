import SwiftUI

struct FolderView: View {
    @StateObject private var viewModel = FolderViewModel()
    @State private var searchText = ""
    @State private var showingSettings = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 搜索栏
                if !viewModel.currentPath.isEmpty {
                    SearchBar(text: $searchText, placeholder: "搜索文件")
                        .padding(.horizontal)
                        .padding(.top, 8)
                }
                
                // 内容区域
                if viewModel.isLoading {
                    LoadingView()
                } else if viewModel.folders.isEmpty && viewModel.files.isEmpty {
                    EmptyFolderView(
                        isRootFolder: viewModel.currentPath.isEmpty,
                        onRefresh: {
                            Task {
                                await viewModel.refresh()
                            }
                        },
                        onSettings: {
                            showingSettings = true
                        }
                    )
                } else {
                    FolderContentView(
                        folders: viewModel.filteredFolders,
                        files: viewModel.filteredFiles,
                        onFolderTap: { folder in
                            Task {
                                await viewModel.enterFolder(folder)
                            }
                        },
                        onFileTap: { file in
                            Task {
                                await viewModel.playFile(file)
                            }
                        },
                        onFileDownload: { file in
                            Task {
                                await viewModel.downloadFile(file)
                            }
                        }
                    )
                }
            }
            .navigationTitle(viewModel.currentFolderName)
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    if !viewModel.currentPath.isEmpty {
                        Button("返回") {
                            Task {
                                await viewModel.goBack()
                            }
                        }
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Menu {
                        Button(action: {
                            Task {
                                await viewModel.refresh()
                            }
                        }) {
                            Label("刷新", systemImage: "arrow.clockwise")
                        }
                        
                        Button(action: {
                            showingSettings = true
                        }) {
                            Label("设置", systemImage: "gearshape")
                        }
                    } label: {
                        Image(systemName: "ellipsis.circle")
                    }
                }
            }
            .refreshable {
                await viewModel.refresh()
            }
            .searchable(text: $searchText, prompt: "搜索文件和文件夹")
            .onChange(of: searchText) { newValue in
                viewModel.searchContent(query: newValue)
            }
            .sheet(isPresented: $showingSettings) {
                SettingView()
            }
            .onAppear {
                Task {
                    await viewModel.loadRootFolder()
                }
            }
        }
    }
}

// MARK: - Folder Content View
struct FolderContentView: View {
    let folders: [OSSFileInfo]
    let files: [OSSFileInfo]
    let onFolderTap: (OSSFileInfo) -> Void
    let onFileTap: (OSSFileInfo) -> Void
    let onFileDownload: (OSSFileInfo) -> Void
    
    var body: some View {
        List {
            // 文件夹部分
            if !folders.isEmpty {
                Section("文件夹") {
                    ForEach(folders, id: \.key) { folder in
                        FolderRowView(folder: folder) {
                            onFolderTap(folder)
                        }
                    }
                }
            }
            
            // 文件部分
            if !files.isEmpty {
                Section("音频文件") {
                    ForEach(files, id: \.key) { file in
                        FileRowView(
                            file: file,
                            onTap: { onFileTap(file) },
                            onDownload: { onFileDownload(file) }
                        )
                    }
                }
            }
        }
        .listStyle(InsetGroupedListStyle())
    }
}

// MARK: - Folder Row View
struct FolderRowView: View {
    let folder: OSSFileInfo
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 12) {
                Image(systemName: "folder.fill")
                    .font(.title2)
                    .foregroundColor(.accentColor)
                    .frame(width: 40)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(folder.fileName)
                        .font(.headline)
                        .foregroundColor(.primary)
                        .lineLimit(1)
                    
                    if let lastModified = folder.lastModified {
                        Text("修改时间: \(lastModified, style: .date)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding(.vertical, 4)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - File Row View
struct FileRowView: View {
    let file: OSSFileInfo
    let onTap: () -> Void
    let onDownload: () -> Void
    @State private var showingActionSheet = false
    
    var body: some View {
        HStack(spacing: 12) {
            // 文件图标
            Image(systemName: fileIcon)
                .font(.title2)
                .foregroundColor(fileIconColor)
                .frame(width: 40)
            
            // 文件信息
            VStack(alignment: .leading, spacing: 4) {
                Text(file.fileName)
                    .font(.headline)
                    .foregroundColor(.primary)
                    .lineLimit(2)
                
                HStack {
                    Text(file.formattedSize)
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    if let lastModified = file.lastModified {
                        Text("• \(lastModified, style: .relative)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
            
            Spacer()
            
            // 操作按钮
            Button(action: { showingActionSheet = true }) {
                Image(systemName: "ellipsis")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding(.vertical, 4)
        .contentShape(Rectangle())
        .onTapGesture {
            onTap()
        }
        .confirmationDialog("选择操作", isPresented: $showingActionSheet) {
            Button("播放") {
                onTap()
            }
            
            Button("下载到本地") {
                onDownload()
            }
            
            Button("取消", role: .cancel) { }
        }
    }
    
    private var fileIcon: String {
        switch file.fileExtension {
        case "mp3", "m4a", "aac":
            return "music.note"
        case "wav", "flac":
            return "waveform"
        case "ogg", "wma":
            return "music.note.list"
        default:
            return "doc.fill"
        }
    }
    
    private var fileIconColor: Color {
        switch file.fileExtension {
        case "mp3", "m4a", "aac", "wav", "flac", "ogg", "wma":
            return .accentColor
        default:
            return .secondary
        }
    }
}

// MARK: - Empty Folder View
struct EmptyFolderView: View {
    let isRootFolder: Bool
    let onRefresh: () -> Void
    let onSettings: () -> Void
    
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: isRootFolder ? "icloud.slash" : "folder")
                .font(.system(size: 80))
                .foregroundColor(.secondary.opacity(0.5))
            
            Text(isRootFolder ? "未连接到云存储" : "文件夹为空")
                .font(.title2)
                .foregroundColor(.secondary)
            
            Text(isRootFolder ? "请先配置阿里云OSS设置" : "此文件夹中没有音频文件")
                .font(.subheadline)
                .foregroundColor(.secondary.opacity(0.8))
                .multilineTextAlignment(.center)
            
            VStack(spacing: 12) {
                if isRootFolder {
                    Button("配置设置") {
                        onSettings()
                    }
                    .buttonStyle(.borderedProminent)
                }
                
                Button("刷新") {
                    onRefresh()
                }
                .buttonStyle(.bordered)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding()
    }
}

// MARK: - Loading View
struct LoadingView: View {
    var body: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.2)
            
            Text("加载中...")
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

// MARK: - Preview
struct FolderView_Previews: PreviewProvider {
    static var previews: some View {
        FolderView()
    }
}
