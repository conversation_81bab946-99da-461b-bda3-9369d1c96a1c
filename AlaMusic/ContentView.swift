import SwiftUI
import CoreData

struct ContentView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @StateObject private var audioPlayerManager = AudioPlayerManager.shared
    
    var body: some View {
        TabView {
            SongListsView()
                .tabItem {
                    Image(systemName: "music.note.list")
                    Text("歌单")
                }
            
            PlayerTabView()
                .tabItem {
                    Image(systemName: audioPlayerManager.isPlaying ? "play.circle.fill" : "play.circle")
                    Text(audioPlayerManager.currentSong != nil ? "正在播放" : "播放")
                }
            
            FolderView()
                .tabItem {
                    Image(systemName: "folder")
                    Text("目录")
                }
            
            SettingView()
                .tabItem {
                    Image(systemName: "gearshape")
                    Text("设置")
                }
        }
        .accentColor(.blue)
    }
}





#Preview {
    ContentView()
        .environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
}
