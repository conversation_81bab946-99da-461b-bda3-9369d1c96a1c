import SwiftUI
import CoreData

struct ContentView: View {
    @Environment(\.managedObjectContext) private var viewContext

    var body: some View {
        TabView {
            Text("歌单")
                .tabItem {
                    Image(systemName: "music.note.list")
                    Text("歌单")
                }

            Text("搜索")
                .tabItem {
                    Image(systemName: "magnifyingglass")
                    Text("搜索")
                }

            Text("播放")
                .tabItem {
                    Image(systemName: "play.circle")
                    Text("播放")
                }

            Text("目录")
                .tabItem {
                    Image(systemName: "folder")
                    Text("目录")
                }

            Text("设置")
                .tabItem {
                    Image(systemName: "gearshape")
                    Text("设置")
                }
        }
        .accentColor(.blue)
    }
}





struct ContentView_Previews: PreviewProvider {
    static var previews: some View {
        ContentView()
            .environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
    }
}
