import Foundation
import CoreData
import UIKit

@objc(Song)
public class Song: NSManagedObject {
    
    // MARK: - Convenience Initializers
    convenience init(context: NSManagedObjectContext, 
                    id: String = UUID().uuidString,
                    title: String,
                    artist: String? = nil,
                    album: String? = nil,
                    filePath: String,
                    fileName: String,
                    fileSize: Int64 = 0,
                    duration: Double = 0.0,
                    format: String? = nil) {
        self.init(context: context)
        
        self.id = id
        self.title = title
        self.artist = artist
        self.album = album
        self.filePath = filePath
        self.fileName = fileName
        self.fileSize = fileSize
        self.duration = duration
        self.format = format
        self.createdAt = Date()
        self.updatedAt = Date()
        self.isLocal = false
        self.isFavorite = false
        self.isAvailable = true
        self.playCount = 0
        self.totalPlayTime = 0.0
    }
    
    // MARK: - Computed Properties
    var formattedDuration: String {
        let minutes = Int(duration) / 60
        let seconds = Int(duration) % 60
        return String(format: "%d:%02d", minutes, seconds)
    }
    
    var displayArtist: String {
        return artist?.isEmpty == false ? artist! : "未知艺术家"
    }
    
    var displayAlbum: String {
        return album?.isEmpty == false ? album! : "未知专辑"
    }
    
    var fileExtension: String {
        return (fileName as NSString).pathExtension.lowercased()
    }
    
    var formattedFileSize: String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useMB, .useKB]
        formatter.countStyle = .file
        return formatter.string(fromByteCount: fileSize)
    }
    
    // MARK: - Status Properties
    var statusIcons: [String] {
        var icons: [String] = []
        
        if isFavorite {
            icons.append("❤️")
        }
        
        if isLocal {
            icons.append("📱")
        } else if isAvailable {
            icons.append("☁️")
        }
        
        return icons
    }
    
    var statusText: String {
        return statusIcons.joined(separator: " ")
    }
    
    // MARK: - Display Properties
    var displayArtist: String {
        return artist?.isEmpty == false ? artist! : "未知艺术家"
    }

    var displayAlbum: String {
        return album?.isEmpty == false ? album! : "未知专辑"
    }

    // MARK: - Album Artwork
    var albumArtworkImage: UIImage? {
        guard let data = albumArtwork else { return nil }
        return UIImage(data: data)
    }
    
    func setAlbumArtwork(_ image: UIImage?) {
        guard let image = image else {
            albumArtwork = nil
            return
        }
        
        // 压缩图片到合适大小
        let targetSize = CGSize(width: 200, height: 200)
        let compressedImage = image.resized(to: targetSize)
        albumArtwork = compressedImage?.jpegData(compressionQuality: 0.8)
    }
    
    // MARK: - Play Statistics
    func markAsPlayed(duration playedDuration: Double = 0.0) {
        playCount += 1
        lastPlayedAt = Date()
        totalPlayTime += playedDuration
        updatedAt = Date()
    }
    
    func toggleFavorite() {
        isFavorite.toggle()
        updatedAt = Date()
    }
    
    // MARK: - Cache Management
    func markAsLocal(localPath: String) {
        self.localPath = localPath
        self.isLocal = true
        self.updatedAt = Date()
    }
    
    func markAsRemote() {
        self.localPath = nil
        self.isLocal = false
        self.updatedAt = Date()
    }
    
    // MARK: - Validation
    func validate() throws {
        guard !title.isEmpty else {
            throw ValidationError.emptyTitle
        }
        
        guard !filePath.isEmpty else {
            throw ValidationError.emptyFilePath
        }
        
        guard !fileName.isEmpty else {
            throw ValidationError.emptyFileName
        }
    }
}

// MARK: - Validation Errors
enum ValidationError: LocalizedError {
    case emptyTitle
    case emptyFilePath
    case emptyFileName
    
    var errorDescription: String? {
        switch self {
        case .emptyTitle:
            return "歌曲标题不能为空"
        case .emptyFilePath:
            return "文件路径不能为空"
        case .emptyFileName:
            return "文件名不能为空"
        }
    }
}

// MARK: - UIImage Extension
extension UIImage {
    func resized(to size: CGSize) -> UIImage? {
        UIGraphicsBeginImageContextWithOptions(size, false, 0.0)
        defer { UIGraphicsEndImageContext() }
        
        draw(in: CGRect(origin: .zero, size: size))
        return UIGraphicsGetImageFromCurrentImageContext()
    }
}
