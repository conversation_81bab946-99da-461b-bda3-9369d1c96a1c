import Foundation

struct OSSFileInfo: Identifiable, Equatable {
    let id = UUID()
    let key: String
    let size: Int64
    let lastModified: Date?
    let etag: String?
    let storageClass: String?
    
    // MARK: - Computed Properties
    var fileName: String {
        return (key as NSString).lastPathComponent
    }
    
    var fileExtension: String {
        return (fileName as NSString).pathExtension.lowercased()
    }
    
    var isDirectory: Bool {
        return key.hasSuffix("/")
    }
    
    var isAudioFile: Bool {
        let audioExtensions = ["mp3", "m4a", "aac", "wav", "flac", "ogg", "wma", "mp4", "m4p"]
        return audioExtensions.contains(fileExtension)
    }
    
    var formattedSize: String {
        let formatter = ByteCountFormatter()
        formatter.countStyle = .file
        return formatter.string(fromByteCount: size)
    }
    
    var parentPath: String {
        if key.hasSuffix("/") {
            let trimmed = String(key.dropLast())
            return (trimmed as NSString).deletingLastPathComponent
        } else {
            return (key as NSString).deletingLastPathComponent
        }
    }
    
    // MARK: - Initializers
    init(key: String, size: Int64 = 0, lastModified: Date? = nil, etag: String? = nil, storageClass: String? = nil) {
        self.key = key
        self.size = size
        self.lastModified = lastModified
        self.etag = etag
        self.storageClass = storageClass
    }
    
    // MARK: - Equatable
    static func == (lhs: OSSFileInfo, rhs: OSSFileInfo) -> Bool {
        return lhs.key == rhs.key
    }
}

// MARK: - OSS Configuration
struct OSSConfiguration {
    let endpoint: String
    let bucketName: String
    let accessKeyId: String
    let accessKeySecret: String
    
    var isValid: Bool {
        return !endpoint.isEmpty &&
               !bucketName.isEmpty &&
               !accessKeyId.isEmpty &&
               !accessKeySecret.isEmpty
    }
}

// MARK: - OSS Error Types
enum OSSError: LocalizedError {
    case invalidConfiguration
    case networkError(String)
    case authenticationError
    case fileNotFound
    case uploadFailed(String)
    case downloadFailed(String)
    case listingFailed(String)
    case unknown(String)
    
    var errorDescription: String? {
        switch self {
        case .invalidConfiguration:
            return "OSS配置无效"
        case .networkError(let message):
            return "网络错误: \(message)"
        case .authenticationError:
            return "认证失败，请检查AccessKey配置"
        case .fileNotFound:
            return "文件未找到"
        case .uploadFailed(let message):
            return "上传失败: \(message)"
        case .downloadFailed(let message):
            return "下载失败: \(message)"
        case .listingFailed(let message):
            return "获取文件列表失败: \(message)"
        case .unknown(let message):
            return "未知错误: \(message)"
        }
    }
}

// MARK: - OSS Operation Result
struct OSSOperationResult<T> {
    let success: Bool
    let data: T?
    let error: OSSError?
    
    init(success: Bool, data: T? = nil, error: OSSError? = nil) {
        self.success = success
        self.data = data
        self.error = error
    }
    
    static func success(_ data: T) -> OSSOperationResult<T> {
        return OSSOperationResult(success: true, data: data)
    }
    
    static func failure(_ error: OSSError) -> OSSOperationResult<T> {
        return OSSOperationResult(success: false, error: error)
    }
}

// MARK: - Download Progress
struct DownloadProgress {
    let bytesDownloaded: Int64
    let totalBytes: Int64
    let percentage: Double
    
    init(bytesDownloaded: Int64, totalBytes: Int64) {
        self.bytesDownloaded = bytesDownloaded
        self.totalBytes = totalBytes
        self.percentage = totalBytes > 0 ? Double(bytesDownloaded) / Double(totalBytes) : 0.0
    }
}

// MARK: - Upload Progress
struct UploadProgress {
    let bytesUploaded: Int64
    let totalBytes: Int64
    let percentage: Double
    
    init(bytesUploaded: Int64, totalBytes: Int64) {
        self.bytesUploaded = bytesUploaded
        self.totalBytes = totalBytes
        self.percentage = totalBytes > 0 ? Double(bytesUploaded) / Double(totalBytes) : 0.0
    }
}
