import Foundation
import Combine

@MainActor
class FolderViewModel: ObservableObject {
    @Published var folders: [OSSFileInfo] = []
    @Published var files: [OSSFileInfo] = []
    @Published var currentPath: String = ""
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    // 搜索过滤后的结果
    @Published var filteredFolders: [OSSFileInfo] = []
    @Published var filteredFiles: [OSSFileInfo] = []
    
    private var pathHistory: [String] = []
    private var cancellables = Set<AnyCancellable>()
    private let ossManager = OSSManager.shared
    private let audioPlayerManager = AudioPlayerManager.shared
    private let cacheManager = CacheManager.shared
    
    var currentFolderName: String {
        if currentPath.isEmpty {
            return "云端文件"
        } else {
            return (currentPath as NSString).lastPathComponent
        }
    }
    
    func loadRootFolder() async {
        currentPath = ""
        pathHistory = []
        await loadCurrentFolder()
    }
    
    func enterFolder(_ folder: OSSFileInfo) async {
        pathHistory.append(currentPath)
        currentPath = folder.key
        await loadCurrentFolder()
    }
    
    func goBack() async {
        guard !pathHistory.isEmpty else { return }
        currentPath = pathHistory.removeLast()
        await loadCurrentFolder()
    }
    
    func refresh() async {
        await loadCurrentFolder()
    }
    
    func searchContent(query: String) {
        let trimmedQuery = query.trimmingCharacters(in: .whitespacesAndNewlines).lowercased()
        
        if trimmedQuery.isEmpty {
            filteredFolders = folders
            filteredFiles = files
        } else {
            filteredFolders = folders.filter { folder in
                folder.fileName.lowercased().contains(trimmedQuery)
            }
            
            filteredFiles = files.filter { file in
                file.fileName.lowercased().contains(trimmedQuery)
            }
        }
    }
    
    func playFile(_ file: OSSFileInfo) async {
        // 创建临时Song对象用于播放
        let song = createSongFromFile(file)
        
        // 获取当前文件夹的所有音频文件作为播放队列
        let audioFiles = files.filter { $0.isAudioFile }
        let songs = audioFiles.map { createSongFromFile($0) }
        
        // 找到当前文件在队列中的位置
        if let currentIndex = audioFiles.firstIndex(where: { $0.key == file.key }) {
            audioPlayerManager.play(songs: songs, startIndex: currentIndex)
        } else {
            audioPlayerManager.play(song: song)
        }
    }
    
    func downloadFile(_ file: OSSFileInfo) async {
        let song = createSongFromFile(file)
        
        do {
            _ = try await cacheManager.downloadSong(song).async()
            // TODO: 显示下载成功提示
        } catch {
            errorMessage = "下载失败: \(error.localizedDescription)"
        }
    }
    
    // MARK: - Private Methods
    private func loadCurrentFolder() async {
        isLoading = true
        errorMessage = nil
        
        do {
            let fileInfos = try await ossManager.listFiles(in: currentPath).async()
            
            // 分离文件夹和文件
            var newFolders: [OSSFileInfo] = []
            var newFiles: [OSSFileInfo] = []
            
            for fileInfo in fileInfos {
                if fileInfo.key.hasSuffix("/") {
                    // 这是一个文件夹
                    newFolders.append(fileInfo)
                } else if fileInfo.isAudioFile {
                    // 这是一个音频文件
                    newFiles.append(fileInfo)
                }
            }
            
            // 排序
            newFolders.sort { $0.fileName < $1.fileName }
            newFiles.sort { $0.fileName < $1.fileName }
            
            folders = newFolders
            files = newFiles
            filteredFolders = newFolders
            filteredFiles = newFiles
            
        } catch {
            errorMessage = "加载文件夹失败: \(error.localizedDescription)"
            folders = []
            files = []
            filteredFolders = []
            filteredFiles = []
        }
        
        isLoading = false
    }
    
    private func createSongFromFile(_ file: OSSFileInfo) -> Song {
        // 这里创建一个临时的Core Data上下文来创建Song对象
        // 在实际应用中，应该使用主上下文或者创建一个专门的方法
        let context = PersistenceController.shared.container.viewContext
        
        let song = Song(context: context,
                       title: extractTitleFromFileName(file.fileName),
                       filePath: file.key,
                       fileName: file.fileName,
                       fileSize: file.size,
                       format: file.fileExtension)
        
        // 尝试从文件名中提取艺术家和专辑信息
        let (artist, album) = extractMetadataFromFileName(file.fileName)
        song.artist = artist
        song.album = album
        
        return song
    }
    
    private func extractTitleFromFileName(_ fileName: String) -> String {
        let nameWithoutExtension = (fileName as NSString).deletingPathExtension
        
        // 尝试解析常见的文件名格式
        // 格式1: "艺术家 - 歌曲名"
        if let range = nameWithoutExtension.range(of: " - ") {
            return String(nameWithoutExtension[range.upperBound...]).trimmingCharacters(in: .whitespacesAndNewlines)
        }
        
        // 格式2: "歌曲名 - 艺术家"
        if let range = nameWithoutExtension.range(of: " - ", options: .backwards) {
            return String(nameWithoutExtension[..<range.lowerBound]).trimmingCharacters(in: .whitespacesAndNewlines)
        }
        
        // 格式3: "序号.歌曲名"
        let components = nameWithoutExtension.components(separatedBy: ".")
        if components.count > 1, let first = components.first, first.allSatisfy({ $0.isNumber }) {
            return components.dropFirst().joined(separator: ".").trimmingCharacters(in: .whitespacesAndNewlines)
        }
        
        // 默认使用完整文件名（不含扩展名）
        return nameWithoutExtension
    }
    
    private func extractMetadataFromFileName(_ fileName: String) -> (artist: String?, album: String?) {
        let nameWithoutExtension = (fileName as NSString).deletingPathExtension
        
        // 尝试解析艺术家信息
        var artist: String?
        
        // 格式1: "艺术家 - 歌曲名"
        if let range = nameWithoutExtension.range(of: " - ") {
            artist = String(nameWithoutExtension[..<range.lowerBound]).trimmingCharacters(in: .whitespacesAndNewlines)
        }
        
        // 从文件路径中提取专辑信息（假设文件夹结构为 艺术家/专辑/歌曲）
        let pathComponents = fileName.components(separatedBy: "/")
        var album: String?
        
        if pathComponents.count >= 3 {
            // 倒数第二个组件可能是专辑名
            album = pathComponents[pathComponents.count - 2]
        }
        
        return (artist, album)
    }
}

// MARK: - Publisher Extension (if not already defined)
extension Publisher {
    func async() async throws -> Output {
        return try await withCheckedThrowingContinuation { continuation in
            var cancellable: AnyCancellable?
            
            cancellable = self
                .sink(
                    receiveCompletion: { completion in
                        switch completion {
                        case .finished:
                            break
                        case .failure(let error):
                            continuation.resume(throwing: error)
                        }
                        cancellable?.cancel()
                    },
                    receiveValue: { value in
                        continuation.resume(returning: value)
                        cancellable?.cancel()
                    }
                )
        }
    }
}
