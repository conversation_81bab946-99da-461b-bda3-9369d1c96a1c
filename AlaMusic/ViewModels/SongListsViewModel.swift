import Foundation
import CoreData
import Combine

@MainActor
class SongListsViewModel: ObservableObject {
    @Published var systemSongLists: [SongList] = []
    @Published var customSongLists: [SongList] = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private var context: NSManagedObjectContext?
    private var cancellables = Set<AnyCancellable>()
    private var songRepository: SongRepository?
    
    func setContext(_ context: NSManagedObjectContext) {
        self.context = context
        self.songRepository = SongRepository(context: context)
        setupSystemSongLists()
    }
    
    func loadSongLists() async {
        guard let context = context else { return }
        
        isLoading = true
        errorMessage = nil
        
        do {
            // 加载自定义歌单
            let customRequest: NSFetchRequest<SongList> = SongList.fetchRequest()
            customRequest.predicate = NSPredicate(format: "type == %@", SongListType.custom.rawValue)
            customRequest.sortDescriptors = [
                NSSortDescriptor(keyPath: \SongList.sortOrder, ascending: true),
                NSSortDescriptor(keyPath: \SongList.createdAt, ascending: false)
            ]
            
            customSongLists = try context.fetch(customRequest)
            
            // 更新系统歌单的歌曲数量
            await updateSystemSongListCounts()
            
        } catch {
            errorMessage = "加载歌单失败: \(error.localizedDescription)"
        }
        
        isLoading = false
    }
    
    func refresh() async {
        await loadSongLists()
    }
    
    func searchSongLists(query: String) {
        guard let context = context else { return }
        
        if query.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            Task {
                await loadSongLists()
            }
            return
        }
        
        do {
            let request: NSFetchRequest<SongList> = SongList.fetchRequest()
            request.predicate = NSCompoundPredicate(andPredicateWithSubpredicates: [
                NSPredicate(format: "type == %@", SongListType.custom.rawValue),
                NSPredicate(format: "name CONTAINS[cd] %@", query)
            ])
            request.sortDescriptors = [
                NSSortDescriptor(keyPath: \SongList.sortOrder, ascending: true),
                NSSortDescriptor(keyPath: \SongList.createdAt, ascending: false)
            ]
            
            customSongLists = try context.fetch(request)
        } catch {
            errorMessage = "搜索歌单失败: \(error.localizedDescription)"
        }
    }
    
    func deleteCustomSongLists(at offsets: IndexSet) async {
        guard let context = context else { return }
        
        for index in offsets {
            let songList = customSongLists[index]
            context.delete(songList)
        }
        
        do {
            try context.save()
            await loadSongLists()
        } catch {
            errorMessage = "删除歌单失败: \(error.localizedDescription)"
        }
    }
    
    // MARK: - Private Methods
    private func setupSystemSongLists() {
        guard let context = context else { return }
        
        let systemTypes: [SystemSongListType] = [.all, .favorites, .recent, .local, .unplayed]
        
        for systemType in systemTypes {
            // 检查是否已存在
            let request: NSFetchRequest<SongList> = SongList.fetchRequest()
            request.predicate = NSCompoundPredicate(andPredicateWithSubpredicates: [
                NSPredicate(format: "type == %@", SongListType.system.rawValue),
                NSPredicate(format: "systemType == %@", systemType.rawValue)
            ])
            request.fetchLimit = 1
            
            do {
                let existingSongLists = try context.fetch(request)
                
                if existingSongLists.isEmpty {
                    // 创建系统歌单
                    let songList = SongList(context: context,
                                          name: systemType.displayName,
                                          type: .system,
                                          systemType: systemType)
                    songList.sortOrder = Int32(systemTypes.firstIndex(of: systemType) ?? 0)
                }
            } catch {
                print("Failed to setup system song list \(systemType): \(error)")
            }
        }
        
        do {
            try context.save()
            loadSystemSongLists()
        } catch {
            print("Failed to save system song lists: \(error)")
        }
    }
    
    private func loadSystemSongLists() {
        guard let context = context else { return }
        
        do {
            let request: NSFetchRequest<SongList> = SongList.fetchRequest()
            request.predicate = NSPredicate(format: "type == %@", SongListType.system.rawValue)
            request.sortDescriptors = [NSSortDescriptor(keyPath: \SongList.sortOrder, ascending: true)]
            
            systemSongLists = try context.fetch(request)
        } catch {
            errorMessage = "加载系统歌单失败: \(error.localizedDescription)"
        }
    }
    
    private func updateSystemSongListCounts() async {
        guard let songRepository = songRepository else { return }
        
        for songList in systemSongLists {
            guard let systemType = songList.systemSongListType else { continue }
            
            do {
                let songs: [Song]
                
                switch systemType {
                case .all:
                    songs = try await songRepository.getAllSongs().async()
                case .favorites:
                    songs = try await songRepository.getFavoriteSongs().async()
                case .recent:
                    songs = try await songRepository.getRecentSongs(limit: 200).async()
                case .local:
                    songs = try await songRepository.getLocalSongs().async()
                case .unplayed:
                    songs = try await songRepository.getUnplayedSongs().async()
                }
                
                songList.songCount = Int32(songs.count)
                
            } catch {
                print("Failed to update song count for \(systemType): \(error)")
            }
        }
        
        // 保存更新
        do {
            try context?.save()
        } catch {
            print("Failed to save song list counts: \(error)")
        }
    }
}

// MARK: - Publisher Extension
extension Publisher {
    func async() async throws -> Output {
        return try await withCheckedThrowingContinuation { continuation in
            var cancellable: AnyCancellable?
            
            cancellable = self
                .sink(
                    receiveCompletion: { completion in
                        switch completion {
                        case .finished:
                            break
                        case .failure(let error):
                            continuation.resume(throwing: error)
                        }
                        cancellable?.cancel()
                    },
                    receiveValue: { value in
                        continuation.resume(returning: value)
                        cancellable?.cancel()
                    }
                )
        }
    }
}
