import Foundation
import CoreData
import Combine

@MainActor
class SongListDetailViewModel: ObservableObject {
    @Published var songs: [Song] = []
    @Published var filteredSongs: [Song] = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private var context: NSManagedObjectContext?
    private var songRepository: SongRepository?
    private var songListRepository: SongListRepository?
    private var cancellables = Set<AnyCancellable>()
    private var currentSongList: SongList?

    func setContext(_ context: NSManagedObjectContext) {
        self.context = context
        self.songRepository = SongRepository(context: context)
        self.songListRepository = SongListRepository(context: context)
    }

    func setSongList(_ songList: SongList) {
        self.currentSongList = songList
        Task {
            await loadSongs(for: songList)
        }
    }
    
    func loadSongs(for songList: SongList) async {
        guard let songRepository = songRepository else { return }
        
        isLoading = true
        errorMessage = nil
        
        do {
            let loadedSongs: [Song]
            
            if songList.type == .system {
                // 系统歌单根据类型动态生成
                loadedSongs = try await loadSystemSongListSongs(songList)
            } else {
                // 自定义歌单从关联关系获取
                loadedSongs = try await loadCustomSongListSongs(songList)
            }
            
            songs = loadedSongs
            filteredSongs = loadedSongs
            
        } catch {
            errorMessage = "加载歌曲失败: \(error.localizedDescription)"
            songs = []
            filteredSongs = []
        }
        
        isLoading = false
    }
    
    func refresh() async {
        guard let currentSongList = currentSongList else { return }
        await loadSongs(for: currentSongList)
    }

    func addSong(_ song: Song) async {
        guard let songList = currentSongList,
              let songListRepository = songListRepository,
              songList.songListType == .custom else { return }

        do {
            try await songListRepository.addSongToSongList(song, songList: songList).async()
            await refresh()
        } catch {
            errorMessage = "添加歌曲失败: \(error.localizedDescription)"
        }
    }

    func toggleFavorite(_ song: Song) async {
        guard let songRepository = songRepository else { return }

        do {
            try await songRepository.toggleFavorite(song).async()
            await refresh()
        } catch {
            errorMessage = "切换收藏状态失败: \(error.localizedDescription)"
        }
    }

    func playAllSongs() {
        guard !songs.isEmpty else { return }

        let audioPlayerManager = AudioPlayerManager.shared
        audioPlayerManager.setPlayQueue(songs)
        audioPlayerManager.playAtIndex(0)
    }

    func shufflePlay() {
        guard !songs.isEmpty else { return }

        let audioPlayerManager = AudioPlayerManager.shared
        audioPlayerManager.setPlayQueue(songs)
        audioPlayerManager.shuffleQueue()
        audioPlayerManager.playAtIndex(0)
    }
    
    func searchSongs(query: String) {
        let trimmedQuery = query.trimmingCharacters(in: .whitespacesAndNewlines).lowercased()
        
        if trimmedQuery.isEmpty {
            filteredSongs = songs
        } else {
            filteredSongs = songs.filter { song in
                song.title.lowercased().contains(trimmedQuery) ||
                song.displayArtist.lowercased().contains(trimmedQuery) ||
                song.displayAlbum.lowercased().contains(trimmedQuery)
            }
        }
    }
    
    func removeSong(_ song: Song) async {
        guard let songList = currentSongList,
              let songListRepository = songListRepository,
              songList.songListType == .custom else { return }

        do {
            try await songListRepository.removeSongFromSongList(song, songList: songList).async()
            await refresh()
        } catch {
            errorMessage = "移除歌曲失败: \(error.localizedDescription)"
        }
    }

    func removeSongs(at offsets: IndexSet) async {
        guard let songList = currentSongList,
              let songListRepository = songListRepository,
              songList.songListType == .custom else { return }

        do {
            for index in offsets {
                let song = filteredSongs[index]
                try await songListRepository.removeSongFromSongList(song, songList: songList).async()
            }
            await refresh()
        } catch {
            errorMessage = "移除歌曲失败: \(error.localizedDescription)"
        }
    }
    
    // MARK: - Private Methods
    private func loadSystemSongListSongs(_ songList: SongList) async throws -> [Song] {
        guard let songRepository = songRepository,
              let systemType = songList.systemSongListType else {
            return []
        }
        
        switch systemType {
        case .all:
            return try await songRepository.getAllSongs().async()
        case .favorites:
            return try await songRepository.getFavoriteSongs().async()
        case .recent:
            return try await songRepository.getRecentSongs(limit: 200).async()
        case .local:
            return try await songRepository.getLocalSongs().async()
        case .unplayed:
            return try await songRepository.getUnplayedSongs().async()
        }
    }
    
    private func loadCustomSongListSongs(_ songList: SongList) async throws -> [Song] {
        guard let context = context else { return [] }
        
        let request: NSFetchRequest<SongListItem> = SongListItem.fetchRequest()
        request.predicate = NSPredicate(format: "songList == %@", songList)
        request.sortDescriptors = [NSSortDescriptor(keyPath: \SongListItem.sortOrder, ascending: true)]
        request.relationshipKeyPathsForPrefetching = ["song"]
        
        let songListItems = try context.fetch(request)
        return songListItems.compactMap { $0.song }
    }
}

// MARK: - Add Songs ViewModel
@MainActor
class AddSongsViewModel: ObservableObject {
    @Published var availableSongs: [Song] = []
    @Published var filteredSongs: [Song] = []
    @Published var selectedSongs: Set<Song> = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private var context: NSManagedObjectContext?
    private var songRepository: SongRepository?
    
    func setContext(_ context: NSManagedObjectContext) {
        self.context = context
        self.songRepository = SongRepository(context: context)
    }
    
    func loadAvailableSongs(excluding songList: SongList) async {
        guard let songRepository = songRepository else { return }
        
        isLoading = true
        errorMessage = nil
        
        do {
            // 获取所有歌曲
            let allSongs = try await songRepository.getAllSongs().async()
            
            // 获取已在歌单中的歌曲
            let existingSongs = Set(getExistingSongs(in: songList))
            
            // 过滤出不在歌单中的歌曲
            availableSongs = allSongs.filter { !existingSongs.contains($0) }
            filteredSongs = availableSongs
            
        } catch {
            errorMessage = "加载歌曲失败: \(error.localizedDescription)"
            availableSongs = []
            filteredSongs = []
        }
        
        isLoading = false
    }
    
    func searchSongs(query: String) {
        let trimmedQuery = query.trimmingCharacters(in: .whitespacesAndNewlines).lowercased()
        
        if trimmedQuery.isEmpty {
            filteredSongs = availableSongs
        } else {
            filteredSongs = availableSongs.filter { song in
                song.title.lowercased().contains(trimmedQuery) ||
                song.displayArtist.lowercased().contains(trimmedQuery) ||
                song.displayAlbum.lowercased().contains(trimmedQuery)
            }
        }
    }
    
    func toggleSongSelection(_ song: Song) {
        if selectedSongs.contains(song) {
            selectedSongs.remove(song)
        } else {
            selectedSongs.insert(song)
        }
    }
    
    func addSelectedSongs(to songList: SongList) async {
        guard let context = context, !selectedSongs.isEmpty else { return }
        
        // 获取当前歌单中的最大排序号
        let maxSortOrder = getMaxSortOrder(in: songList)
        
        // 为每个选中的歌曲创建SongListItem
        for (index, song) in selectedSongs.enumerated() {
            let songListItem = SongListItem(context: context)
            songListItem.song = song
            songListItem.songList = songList
            songListItem.sortOrder = maxSortOrder + Int32(index + 1)
            songListItem.addedAt = Date()
        }
        
        do {
            try context.save()
            
            // 更新歌单的歌曲数量
            songList.songCount += Int32(selectedSongs.count)
            songList.updatedAt = Date()
            try context.save()
            
        } catch {
            errorMessage = "添加歌曲失败: \(error.localizedDescription)"
        }
    }
    
    // MARK: - Private Methods
    private func getExistingSongs(in songList: SongList) -> [Song] {
        guard let songListItems = songList.songListItems?.allObjects as? [SongListItem] else {
            return []
        }
        return songListItems.compactMap { $0.song }
    }
    
    private func getMaxSortOrder(in songList: SongList) -> Int32 {
        guard let songListItems = songList.songListItems?.allObjects as? [SongListItem] else {
            return 0
        }
        return songListItems.map { $0.sortOrder }.max() ?? 0
    }
}

// MARK: - Publisher Extension
extension Publisher {
    func async() async throws -> Output {
        return try await withCheckedThrowingContinuation { continuation in
            var cancellable: AnyCancellable?
            
            cancellable = self
                .sink(
                    receiveCompletion: { completion in
                        switch completion {
                        case .finished:
                            break
                        case .failure(let error):
                            continuation.resume(throwing: error)
                        }
                        cancellable?.cancel()
                    },
                    receiveValue: { value in
                        continuation.resume(returning: value)
                        cancellable?.cancel()
                    }
                )
        }
    }
}
