import Foundation
import Combine

@MainActor
class SettingViewModel: ObservableObject {
    @Published var ossConfigured = false
    @Published var backgroundPlayEnabled = true
    @Published var autoPlayEnabled = false
    @Published var wifiOnlyDownload = true
    @Published var maxCacheSize: Int = 5
    @Published var cacheInfo = "计算中..."
    
    private let cacheManager = CacheManager.shared
    private let ossManager = OSSManager.shared
    private var cancellables = Set<AnyCancellable>()
    
    func loadSettings() {
        // 检查OSS配置状态
        ossConfigured = ossManager.isConfigured
        
        // 加载用户设置
        loadUserSettings()
        
        // 更新缓存信息
        updateCacheInfo()
    }
    
    private func loadUserSettings() {
        let defaults = UserDefaults.standard
        
        backgroundPlayEnabled = defaults.bool(forKey: "backgroundPlayEnabled")
        autoPlayEnabled = defaults.bool(forKey: "autoPlayEnabled")
        wifiOnlyDownload = defaults.bool(forKey: "wifiOnlyDownload")
        maxCacheSize = defaults.integer(forKey: "maxCacheSize")
        
        // 设置默认值
        if maxCacheSize == 0 {
            maxCacheSize = 5
        }
        
        // 监听设置变化并保存
        setupSettingsObservers()
    }
    
    private func setupSettingsObservers() {
        let defaults = UserDefaults.standard
        
        $backgroundPlayEnabled
            .dropFirst()
            .sink { value in
                defaults.set(value, forKey: "backgroundPlayEnabled")
            }
            .store(in: &cancellables)
        
        $autoPlayEnabled
            .dropFirst()
            .sink { value in
                defaults.set(value, forKey: "autoPlayEnabled")
            }
            .store(in: &cancellables)
        
        $wifiOnlyDownload
            .dropFirst()
            .sink { value in
                defaults.set(value, forKey: "wifiOnlyDownload")
            }
            .store(in: &cancellables)
        
        $maxCacheSize
            .dropFirst()
            .sink { value in
                defaults.set(value, forKey: "maxCacheSize")
            }
            .store(in: &cancellables)
    }
    
    private func updateCacheInfo() {
        Task {
            let totalSize = cacheManager.totalCacheSize
            let availableSpace = cacheManager.availableSpace
            
            let formatter = ByteCountFormatter()
            formatter.countStyle = .file
            
            let totalSizeString = formatter.string(fromByteCount: totalSize)
            let availableSpaceString = formatter.string(fromByteCount: availableSpace)
            
            await MainActor.run {
                cacheInfo = "已用 \(totalSizeString) / 可用 \(availableSpaceString)"
            }
        }
    }
}

// MARK: - OSS Config ViewModel
@MainActor
class OSSConfigViewModel: ObservableObject {
    @Published var endpoint = ""
    @Published var bucketName = ""
    @Published var accessKeyId = ""
    @Published var accessKeySecret = ""
    @Published var isTesting = false
    @Published var isSaving = false
    @Published var testResult: TestResult?
    
    struct TestResult {
        let success: Bool
        let message: String
    }
    
    var isFormValid: Bool {
        !endpoint.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
        !bucketName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
        !accessKeyId.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
        !accessKeySecret.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
    }
    
    private let ossManager = OSSManager.shared
    
    func loadCurrentConfiguration() {
        let config = ossManager.currentConfiguration
        endpoint = config.endpoint ?? ""
        bucketName = config.bucketName ?? ""
        accessKeyId = config.accessKeyId ?? ""
        accessKeySecret = config.accessKeySecret ?? ""
    }
    
    func testConnection() async {
        guard isFormValid else { return }
        
        isTesting = true
        testResult = nil
        
        do {
            let config = OSSConfiguration(
                endpoint: endpoint.trimmingCharacters(in: .whitespacesAndNewlines),
                bucketName: bucketName.trimmingCharacters(in: .whitespacesAndNewlines),
                accessKeyId: accessKeyId.trimmingCharacters(in: .whitespacesAndNewlines),
                accessKeySecret: accessKeySecret.trimmingCharacters(in: .whitespacesAndNewlines)
            )
            
            let success = try await ossManager.testConnection(with: config).async()
            
            if success {
                testResult = TestResult(success: true, message: "连接成功")
            } else {
                testResult = TestResult(success: false, message: "连接失败")
            }
        } catch {
            testResult = TestResult(success: false, message: "连接失败: \(error.localizedDescription)")
        }
        
        isTesting = false
    }
    
    func saveConfiguration() async {
        guard isFormValid else { return }
        
        isSaving = true
        
        let config = OSSConfiguration(
            endpoint: endpoint.trimmingCharacters(in: .whitespacesAndNewlines),
            bucketName: bucketName.trimmingCharacters(in: .whitespacesAndNewlines),
            accessKeyId: accessKeyId.trimmingCharacters(in: .whitespacesAndNewlines),
            accessKeySecret: accessKeySecret.trimmingCharacters(in: .whitespacesAndNewlines)
        )
        
        do {
            try await ossManager.configure(with: config).async()
        } catch {
            print("Failed to save OSS configuration: \(error)")
        }
        
        isSaving = false
    }
}
