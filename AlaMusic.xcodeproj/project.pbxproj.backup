// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		A1234567890123456789001 /* AlamusicApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789002 /* AlamusicApp.swift */; };
		A1234567890123456789003 /* ContentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789004 /* ContentView.swift */; };
		A1234567890123456789005 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789006 /* Assets.xcassets */; };
		A1234567890123456789007 /* Preview Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789008 /* Preview Assets.xcassets */; };
		A1234567890123456789009 /* AlaMusic.xcdatamodeld in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789010 /* AlaMusic.xcdatamodeld */; };
		A1234567890123456789011 /* AudioPlayerManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789067 /* AudioPlayerManager.swift */; };
		A1234567890123456789013 /* CacheManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789068 /* CacheManager.swift */; };
		A1234567890123456789015 /* OSSManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789069 /* OSSManager.swift */; };
		A1234567890123456789017 /* CloudStorageManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789070 /* CloudStorageManager.swift */; };
		A1234567890123456789019 /* SongRepository.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789020 /* SongRepository.swift */; };
		A1234567890123456789021 /* SongListRepository.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789022 /* SongListRepository.swift */; };
		A1234567890123456789023 /* Song+CoreDataClass.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789024 /* Song+CoreDataClass.swift */; };
		A1234567890123456789025 /* SongList+CoreDataClass.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789026 /* SongList+CoreDataClass.swift */; };
		A1234567890123456789027 /* SongListItem+CoreDataClass.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789028 /* SongListItem+CoreDataClass.swift */; };
		A1234567890123456789029 /* OSSFileInfo.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789030 /* OSSFileInfo.swift */; };
		A1234567890123456789031 /* SongListsViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789032 /* SongListsViewModel.swift */; };
		A1234567890123456789033 /* FolderViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789034 /* FolderViewModel.swift */; };
		A1234567890123456789035 /* SettingViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789036 /* SettingViewModel.swift */; };
		A1234567890123456789037 /* SongListDetailViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789038 /* SongListDetailViewModel.swift */; };
		A1234567890123456789039 /* SongListsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789040 /* SongListsView.swift */; };
		A1234567890123456789041 /* PlayerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789042 /* PlayerView.swift */; };
		A1234567890123456789043 /* FolderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789044 /* FolderView.swift */; };
		A1234567890123456789045 /* SettingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789046 /* SettingView.swift */; };
		A1234567890123456789047 /* SongListDetailView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789048 /* SongListDetailView.swift */; };
		A1234567890123456789049 /* PlayQueueView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789050 /* PlayQueueView.swift */; };
		A1234567890123456789051 /* LoadingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789052 /* LoadingView.swift */; };
		A1234567890123456789053 /* AddSongsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789054 /* AddSongsView.swift */; };
		A1234567890123456789055 /* FoldersView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789056 /* FoldersView.swift */; };
		A1234567890123456789057 /* FolderDetailView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789058 /* FolderDetailView.swift */; };
		A1234567890123456789059 /* SettingsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789060 /* SettingsView.swift */; };
		A1234567890123456789061 /* OSSConfigView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789062 /* OSSConfigView.swift */; };
		A1234567890123456789063 /* CacheSettingsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789064 /* CacheSettingsView.swift */; };
		A1234567890123456789065 /* SearchView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789066 /* SearchView.swift */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		A1234567890123456789012 /* AlaMusic.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = AlaMusic.app; sourceTree = BUILT_PRODUCTS_DIR; };
		A1234567890123456789002 /* AlamusicApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AlamusicApp.swift; sourceTree = "<group>"; };
		A1234567890123456789004 /* ContentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentView.swift; sourceTree = "<group>"; };
		A1234567890123456789006 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		A1234567890123456789008 /* Preview Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = "Preview Assets.xcassets"; sourceTree = "<group>"; };
		A1234567890123456789011 /* AlaMusic.xcdatamodel */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcdatamodel; path = AlaMusic.xcdatamodel; sourceTree = "<group>"; };
		A1234567890123456789013 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		A1234567890123456789067 /* AudioPlayerManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AudioPlayerManager.swift; sourceTree = "<group>"; };
		A1234567890123456789068 /* CacheManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CacheManager.swift; sourceTree = "<group>"; };
		A1234567890123456789069 /* OSSManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OSSManager.swift; sourceTree = "<group>"; };
		A1234567890123456789070 /* CloudStorageManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CloudStorageManager.swift; sourceTree = "<group>"; };
		A1234567890123456789020 /* SongRepository.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SongRepository.swift; sourceTree = "<group>"; };
		A1234567890123456789022 /* SongListRepository.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SongListRepository.swift; sourceTree = "<group>"; };
		A1234567890123456789024 /* Song+CoreDataClass.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Song+CoreDataClass.swift"; sourceTree = "<group>"; };
		A1234567890123456789026 /* SongList+CoreDataClass.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "SongList+CoreDataClass.swift"; sourceTree = "<group>"; };
		A1234567890123456789028 /* SongListItem+CoreDataClass.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "SongListItem+CoreDataClass.swift"; sourceTree = "<group>"; };
		A1234567890123456789030 /* OSSFileInfo.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OSSFileInfo.swift; sourceTree = "<group>"; };
		A1234567890123456789032 /* SongListsViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SongListsViewModel.swift; sourceTree = "<group>"; };
		A1234567890123456789034 /* FolderViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FolderViewModel.swift; sourceTree = "<group>"; };
		A1234567890123456789036 /* SettingViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SettingViewModel.swift; sourceTree = "<group>"; };
		A1234567890123456789038 /* SongListDetailViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SongListDetailViewModel.swift; sourceTree = "<group>"; };
		A1234567890123456789040 /* SongListsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SongListsView.swift; sourceTree = "<group>"; };
		A1234567890123456789042 /* PlayerView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PlayerView.swift; sourceTree = "<group>"; };
		A1234567890123456789044 /* FolderView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FolderView.swift; sourceTree = "<group>"; };
		A1234567890123456789046 /* SettingView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SettingView.swift; sourceTree = "<group>"; };
		A1234567890123456789048 /* SongListDetailView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SongListDetailView.swift; sourceTree = "<group>"; };
		A1234567890123456789050 /* PlayQueueView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PlayQueueView.swift; sourceTree = "<group>"; };
		A1234567890123456789052 /* LoadingView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LoadingView.swift; sourceTree = "<group>"; };
		A1234567890123456789054 /* AddSongsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AddSongsView.swift; sourceTree = "<group>"; };
		A1234567890123456789056 /* FoldersView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FoldersView.swift; sourceTree = "<group>"; };
		A1234567890123456789058 /* FolderDetailView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FolderDetailView.swift; sourceTree = "<group>"; };
		A1234567890123456789060 /* SettingsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SettingsView.swift; sourceTree = "<group>"; };
		A1234567890123456789062 /* OSSConfigView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OSSConfigView.swift; sourceTree = "<group>"; };
		A1234567890123456789064 /* CacheSettingsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CacheSettingsView.swift; sourceTree = "<group>"; };
		A1234567890123456789066 /* SearchView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SearchView.swift; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		A1234567890123456789014 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		A1234567890123456789015 = {
			isa = PBXGroup;
			children = (
				A1234567890123456789016 /* AlaMusic */,
				A1234567890123456789017 /* Products */,
			);
			sourceTree = "<group>";
		};
		A1234567890123456789017 /* Products */ = {
			isa = PBXGroup;
			children = (
				A1234567890123456789012 /* AlaMusic.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		A1234567890123456789016 /* AlaMusic */ = {
			isa = PBXGroup;
			children = (
				A1234567890123456789002 /* AlamusicApp.swift */,
				A1234567890123456789004 /* ContentView.swift */,
				A1234567890123456789018 /* Views */,
				A1234567890123456789019 /* ViewModels */,
				A1234567890123456789020 /* Models */,
				A1234567890123456789021 /* Managers */,
				A1234567890123456789022 /* UseCases */,
				A1234567890123456789023 /* Repositories */,
				A1234567890123456789024 /* Utils */,
				A1234567890123456789010 /* AlaMusic.xcdatamodeld */,
				A1234567890123456789006 /* Assets.xcassets */,
				A1234567890123456789013 /* Info.plist */,
				A1234567890123456789025 /* Preview Content */,
			);
			path = AlaMusic;
			sourceTree = "<group>";
		};
		A1234567890123456789025 /* Preview Content */ = {
			isa = PBXGroup;
			children = (
				A1234567890123456789008 /* Preview Assets.xcassets */,
			);
			path = "Preview Content";
			sourceTree = "<group>";
		};
		A1234567890123456789018 /* Views */ = {
			isa = PBXGroup;
			children = (
			);
			path = Views;
			sourceTree = "<group>";
		};
		A1234567890123456789019 /* ViewModels */ = {
			isa = PBXGroup;
			children = (
			);
			path = ViewModels;
			sourceTree = "<group>";
		};
		A1234567890123456789020 /* Models */ = {
			isa = PBXGroup;
			children = (
			);
			path = Models;
			sourceTree = "<group>";
		};
		A1234567890123456789021 /* Managers */ = {
			isa = PBXGroup;
			children = (
			);
			path = Managers;
			sourceTree = "<group>";
		};
		A1234567890123456789022 /* UseCases */ = {
			isa = PBXGroup;
			children = (
			);
			path = UseCases;
			sourceTree = "<group>";
		};
		A1234567890123456789023 /* Repositories */ = {
			isa = PBXGroup;
			children = (
			);
			path = Repositories;
			sourceTree = "<group>";
		};
		A1234567890123456789024 /* Utils */ = {
			isa = PBXGroup;
			children = (
			);
			path = Utils;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		A1234567890123456789026 /* AlaMusic */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A1234567890123456789027 /* Build configuration list for PBXNativeTarget "AlaMusic" */;
			buildPhases = (
				A1234567890123456789028 /* Sources */,
				A1234567890123456789014 /* Frameworks */,
				A1234567890123456789029 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = AlaMusic;
			productName = AlaMusic;
			productReference = A1234567890123456789012 /* AlaMusic.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		A1234567890123456789030 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1431;
				LastUpgradeCheck = 1431;
				TargetAttributes = {
					A1234567890123456789026 = {
						CreatedOnToolsVersion = 14.3.1;
					};
				};
			};
			buildConfigurationList = A1234567890123456789031 /* Build configuration list for PBXProject "AlaMusic" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = A1234567890123456789015;
			productRefGroup = A1234567890123456789017 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				A1234567890123456789026 /* AlaMusic */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		A1234567890123456789029 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A1234567890123456789007 /* Preview Assets.xcassets in Resources */,
				A1234567890123456789005 /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		A1234567890123456789028 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A1234567890123456789009 /* AlaMusic.xcdatamodeld in Sources */,
				A1234567890123456789003 /* ContentView.swift in Sources */,
				A1234567890123456789001 /* AlamusicApp.swift in Sources */,
				A1234567890123456789011 /* AudioPlayerManager.swift in Sources */,
				A1234567890123456789013 /* CacheManager.swift in Sources */,
				A1234567890123456789015 /* OSSManager.swift in Sources */,
				A1234567890123456789017 /* CloudStorageManager.swift in Sources */,
				A1234567890123456789019 /* SongRepository.swift in Sources */,
				A1234567890123456789021 /* SongListRepository.swift in Sources */,
				A1234567890123456789023 /* Song+CoreDataClass.swift in Sources */,
				A1234567890123456789025 /* SongList+CoreDataClass.swift in Sources */,
				A1234567890123456789027 /* SongListItem+CoreDataClass.swift in Sources */,
				A1234567890123456789029 /* OSSFileInfo.swift in Sources */,
				A1234567890123456789031 /* SongListsViewModel.swift in Sources */,
				A1234567890123456789033 /* FolderViewModel.swift in Sources */,
				A1234567890123456789035 /* SettingViewModel.swift in Sources */,
				A1234567890123456789037 /* SongListDetailViewModel.swift in Sources */,
				A1234567890123456789039 /* SongListsView.swift in Sources */,
				A1234567890123456789041 /* PlayerView.swift in Sources */,
				A1234567890123456789043 /* FolderView.swift in Sources */,
				A1234567890123456789045 /* SettingView.swift in Sources */,
				A1234567890123456789047 /* SongListDetailView.swift in Sources */,
				A1234567890123456789049 /* PlayQueueView.swift in Sources */,
				A1234567890123456789051 /* LoadingView.swift in Sources */,
				A1234567890123456789053 /* AddSongsView.swift in Sources */,
				A1234567890123456789055 /* FoldersView.swift in Sources */,
				A1234567890123456789057 /* FolderDetailView.swift in Sources */,
				A1234567890123456789059 /* SettingsView.swift in Sources */,
				A1234567890123456789061 /* OSSConfigView.swift in Sources */,
				A1234567890123456789063 /* CacheSettingsView.swift in Sources */,
				A1234567890123456789065 /* SearchView.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		A1234567890123456789032 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.5;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		A1234567890123456789033 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.5;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		A1234567890123456789034 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"AlaMusic/Preview Content\"";
				DEVELOPMENT_TEAM = "";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = AlaMusic/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "阿拉摩音乐";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = UIInterfaceOrientationPortrait;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.alamusic.app;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		A1234567890123456789035 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"AlaMusic/Preview Content\"";
				DEVELOPMENT_TEAM = "";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = AlaMusic/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "阿拉摩音乐";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = UIInterfaceOrientationPortrait;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.alamusic.app;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		A1234567890123456789031 /* Build configuration list for PBXProject "AlaMusic" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A1234567890123456789032 /* Debug */,
				A1234567890123456789033 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A1234567890123456789027 /* Build configuration list for PBXNativeTarget "AlaMusic" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A1234567890123456789034 /* Debug */,
				A1234567890123456789035 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCVersionGroup section */
		A1234567890123456789010 /* AlaMusic.xcdatamodeld */ = {
			isa = XCVersionGroup;
			children = (
				A1234567890123456789011 /* AlaMusic.xcdatamodel */,
			);
			currentVersion = A1234567890123456789011 /* AlaMusic.xcdatamodel */;
			path = AlaMusic.xcdatamodeld;
			sourceTree = "<group>";
			versionGroupType = wrapper.xcdatamodel;
		};
/* End XCVersionGroup section */
	};
	rootObject = A1234567890123456789030 /* Project object */;
}
