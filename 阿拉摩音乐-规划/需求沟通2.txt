🚨 新发现的技术冲突和实现挑战
1. 后台播放权限与文件格式支持的矛盾
问题：您要求支持FLAC、WAV等格式，但iOS后台播放对某些格式支持有限
--前台支持全格式，后台自动转码为MP3/AAC

2. 搜索功能与复选框显示的交互冲突
您选择"始终显示复选框"，但在搜索View中也支持多选，这会带来体验问题：
问题场景：用户在搜索时选中了歌曲，返回原界面后，选中状态如何处理？
--搜索View的选中状态独立，完成操作后清空

3. 预加载策略与"仅在下载时检查网络"的矛盾
问题：您选择"仅在下载时检查网络"，但预加载需要持续监测网络状态
建议调整：至少需要在播放时监测网络状态，以决定是否预加载
--好的

4. 播放列表拖拽排序与播放模式的冲突
问题：随机播放模式下，拖拽排序的意义何在？
--仍然能够拖拽，拖拽影响显示；拖拽只影响原始顺序，不影响随机序列

5. 缓存清理与正在下载文件的冲突
问题：LRU清理时，如果某个文件正在下载中，如何处理？
--下载中的文件标记为保护状态，不参与LRU

🔍 功能细节的深层问题
1. 歌单删除和编辑功能缺失
问题：自建歌单如何删除？如何重命名？如何调整歌单内歌曲顺序？
--进入自建歌单后，右上角有三个竖的点代表设置，点击后可以删除或重命名
--歌单内支持拖拽排序

2. 批量操作的范围不明确
您提到支持批量操作，但具体包括哪些：
批量添加到歌单 ✓（已确认）
批量删除？
--支持
批量下载？
--不需要
批量移除出歌单？
--可有可无

3. 播放统计的具体指标
"播放历史记录"需要记录哪些数据：
播放次数
--需要
最后播放时间
--需要
累计播放时长？
--需要
跳过次数？
--不需要

4. OSS文件变更的检测机制
问题：如何检测OSS上的文件已更新或删除？
--只需要手动点击更新oss列表时才需要检测，或者需要下载前才检测

5. 定时关闭的具体行为
问题：定时关闭是暂停播放还是退出应用？
--暂停播放并保存状态

🎯 性能和体验的潜在问题
1. Core Data初始化的性能问题
--首次从OSS同步大量歌曲时，Core Data插入性能

2. 搜索实时性与性能的平衡
问题：实时搜索在大量歌曲时可能卡顿
--添加防抖动（300ms延迟）+ 搜索结果分页
或者提供更简单的效果实现，实现优先

3. 内存中的播放进度管理
您提到"播放进度只在内存中缓存"：
问题：应用被系统杀死后，用户期望恢复播放位置怎么办？
--不需要恢复播放的进度，只需要恢复播放的列表+播放的歌曲
这些需要恢复的信息需要及时保存

4. 多个View之间的状态同步
问题：收藏状态在多个界面显示，如何保证实时同步？
--使用SwiftUI的@EnvironmentObject或Combine框架

🔒 安全和隐私考虑
1. OSS密钥的额外保护
虽然您选择"加密存储"，但建议增加：
Keychain存储敏感信息
应用锁定后清除内存中的密钥
--暂不需要额外保护

2. 用户数据的备份策略
问题：本地的播放历史、收藏等数据是否需要本地备份？
--暂时不需要

📱 iOS特定的适配问题
1. iPad适配
问题：是否考虑iPad适配？
--先不考虑

2. Dynamic Island适配
问题：iPhone 14 Pro的灵动岛是否需要适配？
--需要考虑，预留扩展空间

3. App Store审核考虑
问题：音乐播放应用需要说明版权
--在应用描述中明确"播放用户自有音乐文件"