### 阿拉摩音乐 iOS 应用开发设计文档

#### 1. 技术架构设计
```mermaid
graph TD
    A[UI层 SwiftUI] --> B[业务逻辑层]
    B --> C[数据层 Core Data]
    B --> D[网络层 OSS SDK]
    B --> E[音频层 AVPlayer]
    D --> F[阿里云OSS]
    E --> G[系统音频服务]
    C --> H[本地缓存]
```

#### 2. 核心模块设计

##### 2.1 播放器引擎 (AudioEngine)
```swift
class AudioPlayerManager: ObservableObject {
    @Published var currentSong: Song?
    private var player: AVQueuePlayer
    private var preloadItems: [AVPlayerItem] = []
    
    // 播放控制
    func play(song: Song) { /* 实现播放逻辑 */ }
    func pause() { /* 暂停逻辑 */ }
    func skipNext() { /* 下一首 */ }
    
    // 预加载管理
    private func preloadNextSongs() {
        // 根据网络状态预加载1-3首
    }
}
```

##### 2.2 云端同步器 (OSSManager)
```swift
class OSSSyncManager {
    private let ossClient: OSSClient
    private var syncTimer: Timer?
    
    // 连接状态管理
    enum ConnectionState {
        case connected, disconnected, configuring
    }
    
    // 文件下载
    func downloadSong(_ song: Song, completion: @escaping (Result<URL, Error>) -> Void) {
        // 实现带MD5校验的下载
    }
    
    // 数据同步
    func startSync() {
        // 实时同步：歌单/收藏
        // 延迟同步：播放历史
    }
}
```

#### 3. 数据模型设计 (Core Data)

##### 3.1 Song 实体
```swift
@objc(Song)
public class Song: NSManagedObject {
    @NSManaged public var id: UUID
    @NSManaged public var title: String
    @NSManaged public var artist: String
    @NSManaged public var album: String?
    @NSManaged public var filePath: String // OSS相对路径
    @NSManaged public var isFavorited: Bool
    @NSManaged public var lastPlayedDate: Date?
    @NSManaged public var playCount: Int32
    @NSManaged public var duration: Double
}
```

##### 3.2 SongList 实体
```swift
@objc(SongList)
public class SongList: NSManagedObject {
    @NSManaged public var id: UUID
    @NSManaged public var name: String
    @NSManaged public var isSystemList: Bool // 系统/自建歌单标识
    @NSManaged public var songs: NSOrderedSet? // 保持排序
}
```

#### 4. 关键界面实现方案

##### 4.1 播放界面 (PlayerView)
```swift
struct PlayerView: View {
    @EnvironmentObject var player: AudioPlayerManager
    
    var body: some View {
        VStack {
            // 顶部导航
            HStack {
                Button(action: dismiss) { Image(systemName: "chevron.down") }
                Text("来自 \(player.currentSource)")
                Spacer()
                NetworkStatusIndicator()
            }
            
            // 歌曲信息
            if player.currentSong != nil {
                AlbumArtView()
                SongInfoView()
                ProgressView()
                PlaybackControls()
            } else {
                // 空白状态
                Text("暂无播放歌曲")
                    .font(.title)
                    .foregroundColor(.secondary)
            }
            
            // 底部操作栏
            PlayerActionBar()
        }
        .gesture(dragGesture) // 下滑返回手势
    }
}
```

##### 4.2 歌单详情 (SongListView)
```swift
struct SongListView: View {
    @ObservedObject var list: SongList
    @State private var selectedSongs = Set<UUID>()
    
    var body: some View {
        List {
            // 操作栏
            HStack {
                Checkbox(isChecked: allSelected) { toggleAllSelection() }
                PlayButton(action: playAll)
                AddToPlaylistButton(disabled: selectedSongs.isEmpty)
            }
            
            // 歌曲列表
            ForEach(list.songsArray) { song in
                SongRow(song: song, isSelected: selectedSongs.contains(song.id))
                    .swipeActions(edge: .trailing) {
                        Button(role: .destructive) {
                            removeSong(song)
                        } label: {
                            Label("删除", systemImage: "trash")
                        }
                    }
            }
            .onMove(perform: moveSongs)
        }
    }
    
    private func moveSongs(from source: IndexSet, to destination: Int) {
        // 实现拖拽排序并保存
    }
}
```

#### 5. 缓存管理实现

##### 5.1 缓存策略实现
```swift
class CacheManager {
    static let shared = CacheManager()
    private let cacheDir: URL
    private let sizeLimit: Int // 用户设置的缓存大小
    
    // LRU缓存清理
    func cleanCacheIfNeeded() {
        let cachedFiles = fetchCachedFiles()
            .sorted(by: { $0.lastPlayedDate > $1.lastPlayedDate })
        
        var totalSize = cachedFiles.reduce(0) { $0 + $1.size }
        while totalSize > sizeLimit && !cachedFiles.isEmpty {
            let oldest = cachedFiles.removeLast()
            try? FileManager.default.removeItem(at: oldest.url)
            totalSize -= oldest.size
        }
    }
    
    // 获取缓存文件路径
    func cachePath(for song: Song) -> URL? {
        // 根据OSS路径生成本地缓存路径
    }
}
```

#### 6. 后台播放集成

##### 6.1 音频会话配置
```swift
func setupAudioSession() {
    do {
        let session = AVAudioSession.sharedInstance()
        try session.setCategory(.playback, mode: .default, policy: .longFormAudio)
        try session.setActive(true)
    } catch {
        print("音频会话配置失败: \(error)")
    }
}
```

##### 6.2 锁屏控制
```swift
func setupNowPlayingInfo() {
    var info = [String: Any]()
    info[MPMediaItemPropertyTitle] = currentSong.title
    info[MPMediaItemPropertyArtist] = currentSong.artist
    
    MPNowPlayingInfoCenter.default().nowPlayingInfo = info
    
    // 远程控制命令
    let commandCenter = MPRemoteCommandCenter.shared()
    commandCenter.playCommand.addTarget { _ in
        player.play()
        return .success
    }
    // 其他命令处理...
}
```

#### 7. 性能优化措施

##### 7.1 列表渲染优化
```swift
// 使用惰性加载和差异化更新
List {
    ForEach(songs) { song in
        SongRow(song: song)
            .listRowBackground(Color.clear)
    }
}
.listStyle(.plain)
.environment(\.defaultMinListRowHeight, 60)
```

##### 7.2 网络请求优化
```swift
// OSS目录分页加载
func loadOSSContents(at path: String, page: Int) async -> [OSSFile] {
    let request = OSSGetBucketRequest()
    request.bucketName = bucketName
    request.prefix = "alamusic/\(path)"
    request.maxKeys = 100 // 分页大小
    request.marker = lastMarker
    
    do {
        let result = try await ossClient.getBucket(request)
        return result.contents
    } catch {
        return []
    }
}
```

#### 8. 错误处理机制

##### 8.1 统一错误处理
```swift
class ErrorHandler {
    static func handle(_ error: Error) {
        switch error {
        case OSSError.networkTimeout:
            showToast("网络超时，请检查连接")
        case CacheError.diskFull:
            showToast("存储空间不足")
            CacheManager.shared.cleanCacheIfNeeded()
        default:
            showToast("操作失败: \(error.localizedDescription)")
        }
    }
}
```

#### 9. 测试方案

##### 9.1 单元测试重点
```swift
class AudioPlayerTests: XCTestCase {
    func testPlaySongTriggersPreload() {
        let player = AudioPlayerManager()
        let song = Song(context: testContext)
        player.play(song: song)
        XCTAssertEqual(player.preloadItems.count, 1) // 移动网络预加载1首
    }
    
    func testCacheCleanupLogic() {
        let manager = CacheManager()
        manager.sizeLimit = 100 // 100MB
        // 添加测试文件...
        manager.cleanCacheIfNeeded()
        XCTAssertLessThan(manager.currentCacheSize, 100)
    }
}
```

##### 9.2 UI测试用例
```swift
class PlaylistUITests: XCTestCase {
    func testCreateNewPlaylist() {
        let app = XCUIApplication()
        app.tabBars["标签栏"].buttons["歌单"].tap()
        app.navigationBars["歌单"].buttons["添加"].tap()
        app.textFields["新建歌单"].typeText("我的最爱")
        app.buttons["创建"].tap()
        XCTAssertTrue(app.staticTexts["我的最爱"].exists)
    }
}
```

#### 10. 部署与发布准备
1. **App Store说明**：
   - 在Info.plist添加`NSAppleMusicUsageDescription`："用于播放您的个人音乐文件"
   - 后台模式：启用"音频、AirPlay和画中画"

2. **设备适配**：
   - 主设备：iPhone 11 (iOS 15.5+)
   - 扩展支持：iPhone 14 Pro+ (Dynamic Island)

3. **依赖库**：
   - 阿里云OSS SDK (`pod 'AliyunOSSiOS'`)
   - SwiftUI扩展库 (`pod 'SwiftUIX'`)

### 阿拉摩音乐 iOS 应用开发设计文档（续）

---

### 11. 核心业务逻辑实现

#### 11.1 播放标签页动态逻辑
```swift
// TabBarController.swift
func setupTabBar() {
    let playTab = UITabBarItem(
        title: playerManager.currentSong != nil ? "正在播放" : "播放",
        image: UIImage(systemName: "play.fill"),
        selectedImage: nil
    )
    
    playTab.actionHandler = { [weak self] in
        guard let self = self else { return }
        if playerManager.currentSong != nil {
            self.showPlayerView()
        } else {
            self.showEmptyPlayerView()
        }
    }
}

// 空白播放界面
func showEmptyPlayerView() {
    let emptyView = EmptyPlayerView()
    let hostingController = UIHostingController(rootView: emptyView)
    present(hostingController, animated: true)
}
```

#### 11.2 歌单自动播放逻辑
```swift
// PlaylistManager.swift
func autoPlayFirstSongIfNeeded() {
    guard playerManager.currentSong == nil else { return }
    
    // 1. 尝试播放"最近播放"的第一首
    if let recentList = getSystemPlaylist(.recentlyPlayed),
       let firstSong = recentList.songsArray.first {
        playerManager.play(song: firstSong)
        return
    }
    
    // 2. 尝试播放"全部"歌单的第一首
    if let allSongsList = getSystemPlaylist(.all),
       let firstSong = allSongsList.songsArray.first {
        playerManager.play(song: firstSong)
    }
}
```

---

### 12. 云端文件索引管理

#### 12.1 OSS 文件索引流程
```swift
// OSSSyncManager.swift
func updateSongIndex() async {
    // 分页获取OSS文件列表
    var marker: String? = nil
    var allFiles: [OSSFile] = []
    
    repeat {
        let result = await ossClient.listObjects(prefix: "alamusic/", marker: marker)
        allFiles.append(contentsOf: result.contents)
        marker = result.nextMarker
    } while marker != nil
    
    // 更新Core Data索引
    await MainActor.run {
        let context = coreDataManager.newBackgroundContext()
        context.perform {
            for file in allFiles {
                // 仅处理支持的音乐格式
                guard let fileType = file.key.split(separator: ".").last,
                      ["mp3", "m4a", "flac", "wav"].contains(fileType.lowercased()) 
                else { continue }
                
                // 创建或更新Song对象
                let song = Song.findOrCreate(
                    byPath: file.key, 
                    in: context
                )
                song.title = file.key.fileNameWithoutExtension
                song.fileSize = Int64(file.size)
                song.lastModified = file.lastModified
            }
            try? context.save()
        }
    }
}
```

---

### 13. 缓存系统详细设计

#### 13.1 分层缓存目录结构
```
Caches/
└── alamusic_cache/
    └── my-music-bucket/
        ├── Artists/
        │   ├── Artist_A/
        │   │   ├── Album_X/
        │   │   │   ├── song1.mp3
        │   │   │   └── song2.flac
        │   │   └── Single/
        │   │       └── single1.m4a
        ├── Playlists/
        │   └── Workout/
        │       └── energysong.mp3
        └── Downloads/
            └── new_song.wav
```

#### 13.2 缓存状态管理
```swift
// Song+Cache.swift
extension Song {
    var cacheState: CacheState {
        if let localPath = CacheManager.shared.localPath(for: self),
           FileManager.default.fileExists(atPath: localPath.path) {
            return .cached
        }
        
        if CacheManager.shared.isDownloading(self) {
            return .downloading(progress: CacheManager.shared.downloadProgress(for: self))
        }
        
        return .remote
    }
    
    enum CacheState {
        case cached
        case downloading(progress: Double)
        case remote
    }
}
```

---

### 14. 播放统计实现

#### 14.1 播放事件追踪
```swift
// PlaybackTracker.swift
class PlaybackTracker {
    static let shared = PlaybackTracker()
    private var currentStartTime: Date?
    
    func startTracking(song: Song) {
        // 更新播放次数和最后播放时间
        coreDataManager.updateSongPlayCount(song)
        
        // 开始计时
        currentStartTime = Date()
    }
    
    func stopTracking() {
        guard let startTime = currentStartTime else { return }
        
        // 计算播放时长
        let duration = Date().timeIntervalSince(startTime)
        coreDataManager.addPlayDuration(duration)
        currentStartTime = nil
    }
}

// 在播放器事件中调用
playerManager.onPlay = { song in
    PlaybackTracker.shared.startTracking(song: song)
}

playerManager.onPause = {
    PlaybackTracker.shared.stopTracking()
}
```

---

### 15. 设置模块实现

#### 15.1 缓存清理界面
```swift
// CacheSettingsView.swift
struct CacheSettingsView: View {
    @AppStorage("cacheSizeLimit") var cacheSizeLimit: Int = 1000 // 默认1GB
    
    var body: some View {
        Form {
            Section(header: Text("缓存设置")) {
                Picker("缓存上限", selection: $cacheSizeLimit) {
                    Text("不缓存").tag(0)
                    Text("100 MB").tag(100)
                    Text("500 MB").tag(500)
                    Text("1 GB").tag(1000)
                    Text("2 GB").tag(2000)
                }
                
                Button("立即清理缓存") {
                    CacheManager.shared.cleanCache(aggressively: true)
                }
                
                Text("当前缓存: \(ByteCountFormatter.string(fromByteCount: CacheManager.shared.currentCacheSize, countStyle: .file))")
            }
            
            Section(header: Text("流量统计")) {
                Text("本月已用: \(DataUsageTracker.shared.monthlyUsage)")
                Text("移动网络已用: \(DataUsageTracker.shared.cellularUsage)")
            }
        }
    }
}
```

---

### 16. 动态岛适配实现

#### 16.1 Live Activity 集成
```swift
// DynamicIslandManager.swift
@available(iOS 16.1, *)
class DynamicIslandManager {
    static let shared = DynamicIslandManager()
    private var activity: Activity<MusicPlayerAttributes>?
    
    func startLiveActivity(for song: Song) {
        let attributes = MusicPlayerAttributes()
        let state = MusicPlayerAttributes.ContentState(
            songTitle: song.title,
            artist: song.artist,
            albumArt: song.albumArt
        )
        
        activity = try? Activity.request(
            attributes: attributes,
            contentState: state,
            pushType: nil
        )
    }
    
    func updateLiveActivity(with progress: Double) {
        Task {
            await activity?.update(using: .init(progress: progress))
        }
    }
    
    func endLiveActivity() {
        Task {
            await activity?.end(dismissalPolicy: .immediate)
        }
    }
}
```

---

### 17. 错误监控系统

#### 17.1 统一错误处理框架
```swift
// ErrorMonitor.swift
enum AppError: Error {
    case ossConnectionFailed
    case downloadFailed(song: Song)
    case playbackFailed(error: Error)
    case diskFull
    case invalidFileFormat
}

class ErrorMonitor {
    static let shared = ErrorMonitor()
    private var toastQueue = PriorityQueue<ToastMessage>()
    
    func log(_ error: AppError) {
        // 1. 记录到本地日志
        Logger.error("\(error)")
        
        // 2. 根据错误类型决定是否显示Toast
        switch error {
        case .ossConnectionFailed, .diskFull:
            showToast(message: error.localizedDescription, priority: .high)
        default:
            showToast(message: error.localizedDescription, priority: .medium)
        }
        
        // 3. 执行恢复操作
        handleRecovery(for: error)
    }
    
    private func handleRecovery(for error: AppError) {
        switch error {
        case .diskFull:
            CacheManager.shared.cleanCache(aggressively: true)
        case .playbackFailed:
            playerManager.skipToNext()
        default:
            break
        }
    }
}
```

---

### 18. 性能优化方案

#### 18.1 Core Data 批量处理优化
```swift
// CoreDataManager.swift
func batchInsertSongs(_ songs: [OSSFile]) {
    let context = newBackgroundContext()
    context.perform {
        // 创建批量插入请求
        let insertRequest = NSBatchInsertRequest(
            entity: Song.entity(),
            objects: songs.map { songDict in
                [
                    "filePath": songDict.key,
                    "fileSize": songDict.size,
                    "lastModified": songDict.lastModified
                ]
            }
        )
        
        // 配置合并策略
        insertRequest.resultType = .objectIDs
        insertRequest.affectedStores = persistentContainer.persistentStoreCoordinator.persistentStores
        
        // 执行批量插入
        do {
            let result = try context.execute(insertRequest) as? NSBatchInsertResult
            if let objectIDs = result?.result as? [NSManagedObjectID] {
                // 合并变更到视图上下文
                let changes = [NSInsertedObjectsKey: objectIDs]
                NSManagedObjectContext.mergeChanges(fromRemoteContextSave: changes, into: [viewContext])
            }
        } catch {
            ErrorMonitor.shared.log(.coreDataError(error))
        }
    }
}
```

---

### 19. 测试覆盖率计划

#### 19.1 单元测试覆盖矩阵
| 模块 | 测试用例 | 覆盖率目标 |
|------|---------|-----------|
| 播放器 | 播放控制、预加载、状态恢复 | 95% |
| 缓存系统 | LRU清理、下载管理、空间计算 | 90% |
| 云端同步 | OSS连接、文件索引、冲突处理 | 85% |
| 数据模型 | CRUD操作、关系管理 | 100% |
| UI组件 | 关键交互、状态绑定 | 80% |

#### 19.2 UI 测试关键路径
```swift
func testFullUserJourney() {
    // 1. 配置OSS连接
    app.launch()
    app.tabBars.buttons["设置"].tap()
    app.buttons["配置阿里云"].tap()
    fillOSSForm()
    
    // 2. 浏览目录并播放
    app.tabBars.buttons["目录"].tap()
    app.cells["Artists"].tap()
    app.cells["Artist_A"].tap()
    app.cells["song1.mp3"].tap()
    
    // 3. 添加到歌单
    app.buttons["添加到歌单"].tap()
    app.buttons["新建歌单"].tap()
    app.textFields["新建歌单"].typeText("我的最爱")
    app.buttons["创建"].tap()
    
    // 4. 验证歌单
    app.tabBars.buttons["歌单"].tap()
    XCTAssertTrue(app.staticTexts["我的最爱"].exists)
}
```

---

### 20. 部署与发布清单

#### 20.1 预发布检查表
1. **证书配置**
   - 启用后台音频模式
   - 添加App Transport Security例外
   
2. **性能优化**
   - 启用编译优化 (`-Osize`)
   - 检查内存泄漏 (Instruments)
   - 测试低内存场景

3. **本地化准备**
   - 基础英文文案
   - 中文简体文案
   - 预留本地化扩展点

4. **App Store 元数据**
   - 说明："用于播放您存储在阿里云OSS上的个人音乐收藏"
   - 关键词："音乐播放器, OSS播放器, 私人音乐库"

#### 20.2 依赖库清单
```ruby
# Podfile
target 'AlamoMusic' do
  pod 'AliyunOSSiOS', '~> 2.10.18'
  pod 'SwiftUIX', '~> 0.1.9'
  pod 'CombineExt', '~> 1.8.0'
end
```

---

> 本设计文档完整覆盖了需求文档的所有功能点和技术要求，包含：
> - 架构设计
> - 核心模块实现方案
> - 数据模型定义
> - 性能优化策略
> - 错误处理机制
> - 测试覆盖计划
> - 部署发布清单

如需生成特定模块的详细类图或序列图，请告知具体模块名称。