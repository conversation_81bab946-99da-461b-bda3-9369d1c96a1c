1. 技术栈兼容性问题
问题：SwiftUI在iOS 15.5上的限制
--使用SwiftUI 3.0的功能集（iOS 15兼容），避免使用iOS 16+的新特性

2. 数据存储策略不明确
问题：歌曲信息的本地存储方式
--使用Core Data存储歌曲元数据，UserDefaults存储配置信息

问题：播放历史和收藏的持久化
--使用Core Data关联表记录播放时间、播放次数、收藏状态

3. 播放功能细节不清晰
问题：播放队列管理
--实现完整的播放队列，支持随机播放、单曲循环、列表循环

问题：后台播放支持
--支持MP3、M4A、FLAC、WAV等常见格式

4. 网络和缓存策略
问题：下载队列管理
--简单的单个文件下载

问题：缓存清理策略
--LRU（最近最少使用）算法自动清理超出限制的缓存

问题：网络状态检测
--仅在下载时检查网络

5. UI/UX细节优化
问题：搜索功能缺失
--在歌单和目录页面添加搜索栏，支持歌名、歌手、专辑搜索
点击搜索栏的输入框后，自动启用新的view来展示搜索结果，
搜索框样式：
--类似iOS原生搜索框，点击后展开
搜索结果展示：
--搜索框点击后，就弹出搜索view
	该view中有搜索框、取消按钮
	搜索框中输入内容实时搜索
	取消按钮点击后，返回原界面

问题：批量操作的交互方式
--始终显示复选框（先用这种简单方式实现，后续再考虑优化效果）

问题：删除操作
--支持滑动删除单个项目，批量删除需要确认

6. 阿里云OSS集成细节
问题：权限和安全
--加密存储Access Key（安全性较低）（后续考虑安全优化）

问题：大文件列表处理
--分页加载，每次加载100-200个文件

问题：文件夹结构扫描深度
--不限制深度

7. 错误处理和用户反馈
问题：各种错误状态的处理
--统一的错误提示组件，区分网络错误、文件错误、权限错误等

问题：加载状态展示
建议选择：
推荐：骨架屏加载动画、进度条、下拉刷新
其他选择：简单的loading指示器
--我不太了解，最好能用骨架屏加载动画、进度条、下拉刷新，但是如果实现难度较大，还是优先实现

8. 性能优化考虑
问题：大量歌曲时的列表性能
--使用LazyVStack/List虚拟滚动，图片异步加载和缓存

问题：专辑封面处理
--直接使用原图（下一阶段优化时考虑：生成多种尺寸缩略图，使用内存和磁盘两级缓存）

9. 其他功能建议
问题：歌词支持
--暂不支持歌词（需要留着能力，后期会支持）

问题：均衡器和音效
--不支持音效调节

问题：定时关闭
--支持定时关闭功能（15/30/60分钟）