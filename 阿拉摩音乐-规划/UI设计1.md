# 阿拉摩音乐 iOS 应用 UI 设计文档

## 1. 设计概述

### 1.1 设计原则
- **简洁清晰**：遵循 iOS 设计规范，保持界面简洁
- **易用性优先**：核心功能一目了然，操作路径清晰
- **视觉层次**：通过大小、颜色、间距建立清晰的信息层级
- **一致性**：保持交互模式和视觉元素的一致性

### 1.2 设计风格
- 采用 iOS 15+ 原生设计语言
- 以白色为主的明亮界面，配合系统默认蓝色强调色
- 使用 SF Symbols 系统图标
- 支持深色模式（Dark Mode）

## 2. 信息架构

### 2.1 整体结构图
```mermaid
graph TD
    A[TabBar 底部导航] --> B[歌单]
    A --> C[播放]
    A --> D[目录]
    A --> E[设置]
    
    B --> B1[歌单列表页]
    B1 --> B2[歌单详情页]
    B1 --> B3[搜索视图]
    B2 --> B4[添加到歌单]
    B2 --> B5[新建歌单]
    
    C --> C1[播放界面]
    C1 --> C2[播放列表]
    C1 --> B4
    
    D --> D1[目录浏览]
    D1 --> D2[子目录]
    D1 --> B4
    
    E --> E1[设置页面]
    E1 --> E2[阿里云配置]
```

## 3. 核心页面设计

### 3.1 底部标签栏（TabBar）
```
┌─────────────────────────────────────────────┐
│                                             │
│              [主要内容区域]                   │
│                                             │
├─────────────────────────────────────────────┤
│  歌单      播放      目录      设置          │
│  ⊞        ▶️        📁       ⚙️           │
└─────────────────────────────────────────────┘
```
- 高度：49pt（标准iOS TabBar高度）
- 图标大小：25pt x 25pt
- 选中态：系统蓝色 + 文字高亮
- "播放"标签动态文案：
  - 无播放时：显示"播放"
  - 有播放时：显示"正在播放"（可考虑加动画效果）

### 3.2 歌单页面（SongListsView）

#### 线框图
```
┌─────────────────────────────────────────────┐
│ < 返回          歌单                        │
├─────────────────────────────────────────────┤
│ [🔍 搜索歌曲、歌手、专辑]                    │
├─────────────────────────────────────────────┤
│ 系统歌单                                     │
├─────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────┐ │
│ │ ❤️  我的收藏              999首    ▶️   │ │
│ └─────────────────────────────────────────┘ │
│ ┌─────────────────────────────────────────┐ │
│ │ 🕐  最近播放              200首    ▶️   │ │
│ └─────────────────────────────────────────┘ │
│ ┌─────────────────────────────────────────┐ │
│ │ 📱  本地歌曲              150首    ▶️   │ │
│ └─────────────────────────────────────────┘ │
│ ┌─────────────────────────────────────────┐ │
│ │ 🎵  全部                 1234首   ▶️   │ │
│ └─────────────────────────────────────────┘ │
│ ┌─────────────────────────────────────────┐ │
│ │ ⭕  未播放过              100首    ▶️   │ │
│ └─────────────────────────────────────────┘ │
├─────────────────────────────────────────────┤
│ 自建歌单 (5)                         +      │
├─────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────┐ │
│ │ [🎵] 我的最爱              50首    ▶️   │ │
│ └─────────────────────────────────────────┘ │
│ ┌─────────────────────────────────────────┐ │
│ │ [🎵] 运动歌单              30首    ▶️   │ │
│ └─────────────────────────────────────────┘ │
└─────────────────────────────────────────────┘
```

#### 组件说明
- **搜索栏**：iOS原生搜索栏样式，点击展开全屏搜索
- **歌单项**：
  - 高度：60pt
  - 左侧图标：40pt x 40pt
  - 右侧播放按钮：44pt x 44pt 触摸区域
  - 分隔线：左侧缩进16pt

### 3.3 歌单详情页（SongListView）

#### 线框图
```
┌─────────────────────────────────────────────┐
│ < 返回     歌单名称              ⋯         │
├─────────────────────────────────────────────┤
│          ┌─────────────────┐                │
│          │                 │                │
│          │   专辑封面图     │                │
│          │   200x200       │                │
│          │                 │                │
│          └─────────────────┘                │
│            歌单名称                          │
│            共 50 首歌曲                      │
├─────────────────────────────────────────────┤
│ ☐ 全选    ▶️ 播放    + 添加到              │
├─────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────┐ │
│ │ ☐  歌曲名称                              │ │
│ │    歌手名 • 专辑名                        │ │
│ │    /music/folder/song.mp3                │ │
│ │                         ❤️ 📱 ☁️        │ │
│ └─────────────────────────────────────────┘ │
│ ┌─────────────────────────────────────────┐ │
│ │ ☐  很长的歌曲名称需要截断处理...          │ │
│ │    歌手名 • 专辑名                        │ │
│ │    /music/folder/subfolder/song.mp3      │ │
│ │                         ❤️ 📱 ☁️        │ │
│ └─────────────────────────────────────────┘ │
└─────────────────────────────────────────────┘
```

#### 组件说明
- **顶部封面区**：
  - 封面图：200pt x 200pt，圆角8pt
  - 垂直间距：20pt
- **操作栏**：
  - 高度：44pt
  - 按钮间距：均分布局
  - 禁用态：灰色 + 50%透明度
- **歌曲列表项**：
  - 高度：80pt
  - 复选框：始终显示，24pt x 24pt
  - 图标说明：
    - ❤️ 已收藏（红色实心）
    - 📱 已缓存到本地
    - ☁️ 云端文件（可用/不可用通过颜色区分）
  - 文件路径：12pt字号，灰色文字

### 3.4 播放界面（PlayerView）

#### 线框图
```
┌─────────────────────────────────────────────┐
│  ↓               来自 歌单名称        🌐    │
├─────────────────────────────────────────────┤
│                                             │
│          ┌─────────────────┐                │
│          │                 │                │
│          │                 │                │
│          │   专辑封面/图标   │                │
│          │    300x300      │                │
│          │                 │                │
│          │                 │                │
│          └─────────────────┘                │
│                                             │
│              歌曲名称                        │
│              歌手名称                        │
│              专辑名称                        │
│                                             │
│         ━━━━━━━━━━━━━━━━━━━━━━━             │
│         01:23              04:56            │
│                                             │
│     🔀    ⏮     ▶️/⏸     ⏭               │
│                                             │
│                                             │
│     ❤️        +歌单        ≡列表           │
│                                             │
└─────────────────────────────────────────────┘
```

#### 组件说明
- **顶部栏**：
  - 返回按钮：下箭头，44pt触摸区域
  - 网络状态图标：WiFi/4G/无网络，仅展示
- **封面区域**：
  - 大小：300pt x 300pt
  - 圆角：12pt
  - 无封面时显示：music.note系统图标
- **进度条**：
  - 可拖动调整进度
  - 高度：4pt，触摸区域44pt
- **控制按钮**：
  - 主按钮（播放/暂停）：60pt
  - 上下首按钮：44pt
  - 播放模式按钮：30pt（左下角）

### 3.5 目录浏览（FolderView）

#### 线框图
```
┌─────────────────────────────────────────────┐
│  🏠 > music > subfolder                     │
├─────────────────────────────────────────────┤
│ ☐ 全选    ▶️ 播放    + 添加到              │
├─────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────┐ │
│ │ ☐ 📁 子文件夹名称           包含12首 >   │ │
│ └─────────────────────────────────────────┘ │
│ ┌─────────────────────────────────────────┐ │
│ │ ☐ 🎵 歌曲文件.mp3           3.5MB       │ │
│ └─────────────────────────────────────────┘ │
│ ┌─────────────────────────────────────────┐ │
│ │ ☐ 🎵 另一首歌.flac          45.2MB      │ │
│ └─────────────────────────────────────────┘ │
└─────────────────────────────────────────────┘
```

### 3.6 设置页面（SettingView）

#### 线框图
```
┌─────────────────────────────────────────────┐
│              设置                           │
├─────────────────────────────────────────────┤
│ 阿里云配置                                   │
│ ┌─────────────────────────────────────────┐ │
│ │ 阿里云OSS              已连接 ✅     >   │ │
│ │ 更新歌曲列表           [立即更新]         │ │
│ └─────────────────────────────────────────┘ │
├─────────────────────────────────────────────┤
│ 播放与下载                                   │
│ ┌─────────────────────────────────────────┐ │
│ │ 边听边存                        [开关]   │ │
│ │ 音乐缓存上限              1GB        >   │ │
│ └─────────────────────────────────────────┘ │
├─────────────────────────────────────────────┤
│ 流量统计                                     │
│ ┌─────────────────────────────────────────┐ │
│ │ 本月已用流量              235.6 MB       │ │
│ │ 移动流量                  56.3 MB        │ │
│ └─────────────────────────────────────────┘ │
├─────────────────────────────────────────────┤
│ 其他                                         │
│ ┌─────────────────────────────────────────┐ │
│ │ 定时关闭                  关闭       >   │ │
│ └─────────────────────────────────────────┘ │
└─────────────────────────────────────────────┘
```

## 4. 弹出视图设计

### 4.1 搜索视图（全屏）
```
┌─────────────────────────────────────────────┐
│ [🔍 搜索输入框]                    取消     │
├─────────────────────────────────────────────┤
│ 搜索结果                                     │
├─────────────────────────────────────────────┤
│ （与歌单列表项相同的布局）                    │
│                                             │
│ 或                                          │
│                                             │
│        没有找到与"xxx"相关的结果              │
│                                             │
└─────────────────────────────────────────────┘
```

### 4.2 播放列表视图（底部弹出，3/4屏幕）
```
┌─────────────────────────────────────────────┐
│ ═══════════════                             │
│           播放列表 (25首)                    │
├─────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────┐ │
│ │ 1. 歌曲名称                         ≡    │ │
│ │    歌手名                                │ │
│ └─────────────────────────────────────────┘ │
│ ┌─────────────────────────────────────────┐ │
│ │ 2. 当前播放的歌曲 🎵                ≡    │ │
│ │    歌手名                                │ │
│ └─────────────────────────────────────────┘ │
└─────────────────────────────────────────────┘
```

### 4.3 添加到歌单视图（底部弹出，3/4屏幕）
```
┌─────────────────────────────────────────────┐
│ ═══════════════                             │
│      添加到歌单              取消           │
├─────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────┐ │
│ │ + 新建歌单                               │ │
│ └─────────────────────────────────────────┘ │
│ ┌─────────────────────────────────────────┐ │
│ │ 🎵 我的最爱                   50首       │ │
│ └─────────────────────────────────────────┘ │
│ ┌─────────────────────────────────────────┐ │
│ │ 🎵 运动歌单                   30首       │ │
│ └─────────────────────────────────────────┘ │
└─────────────────────────────────────────────┘
```

### 4.4 新建歌单视图（底部弹出，1/2屏幕）
```
┌─────────────────────────────────────────────┐
│ ═══════════════                             │
│      取消         新建歌单          创建     │
├─────────────────────────────────────────────┤
│                                             │
│ 歌单名称                                     │
│ ┌─────────────────────────────────────────┐ │
│ │ 新建歌单1                               │ │
│ └─────────────────────────────────────────┘ │
│                                             │
└─────────────────────────────────────────────┘
```

## 5. 核心交互流程

### 5.1 播放歌曲流程
1. 用户在任意列表点击歌曲（非复选框区域）
2. 系统检查本地缓存
3. 有缓存：直接播放
4. 无缓存：显示加载状态，从云端下载
5. 下载成功：开始播放，更新播放统计
6. 下载失败：Toast提示，自动跳到下一首

### 5.2 批量操作流程
1. 用户点击全选或单个复选框
2. 操作栏按钮变为可用状态
3. 用户点击"添加到歌单"
4. 弹出歌单选择视图
5. 选择歌单后自动返回
6. Toast提示"已添加X首歌曲到XX歌单"

### 5.3 搜索流程
1. 点击搜索框
2. 展开全屏搜索视图，键盘自动弹出
3. 输入搜索词（300ms防抖）
4. 实时显示搜索结果
5. 点击结果项播放或进行批量操作
6. 点击取消返回歌单页

## 6. 主要UI组件清单

### 6.1 导航组件
- **TabBar**：底部固定导航栏
- **NavigationBar**：顶部导航栏，含返回按钮
- **面包屑导航**：用于目录浏览的路径导航

### 6.2 列表组件
- **歌单列表项**：显示图标、名称、数量、播放按钮
- **歌曲列表项**：复选框、歌曲信息、状态图标
- **文件列表项**：文件/文件夹图标、名称、大小/数量

### 6.3 控制组件
- **播放控制按钮组**：上一首、播放/暂停、下一首
- **进度条**：可拖动的音乐播放进度
- **音量滑块**：系统音量控制
- **开关**：iOS原生UISwitch
- **复选框**：圆形选择框

### 6.4 弹出组件
- **底部抽屉**：用于播放列表、歌单选择等
- **全屏模态**：用于搜索视图
- **Toast**：轻量级提示信息

### 6.5 输入组件
- **搜索框**：iOS原生搜索栏样式
- **文本输入框**：用于歌单命名等

## 7. 不明确或存在歧义的UI设计点

### 7.1 播放按钮的交互行为
**需求描述**："播放按钮（播放整个歌单）"、"播放按钮（播放目录下所有音乐，包含子目录）"

**可能的设计方案**：
1. **方案A - 立即播放**：点击后立即开始播放第一首歌
   - 优点：响应快速，符合用户预期
   - 缺点：可能打断当前播放
   
2. **方案B - 播放确认**：点击后弹出选项（立即播放/添加到播放列表末尾）
   - 优点：给用户更多控制权
   - 缺点：增加操作步骤

**建议**：采用方案A，符合主流音乐App的交互习惯

**需要确认**：是否需要保留当前播放列表的选项？

### 7.2 歌单封面图片来源
**需求描述**："图标：歌单内第一首歌的专辑封面"

**可能的设计方案**：
1. **方案A - 动态获取**：每次都从第一首歌提取封面
   - 优点：始终保持最新
   - 缺点：性能开销，可能没有封面信息
   
2. **方案B - 缓存机制**：首次提取后缓存，歌单变化时更新
   - 优点：性能好
   - 缺点：需要额外的缓存管理

**建议**：采用方案B，并在无封面时显示默认图标

**需要确认**：专辑封面的获取方式（从音频文件元数据？）

### 7.3 网络状态图标的具体展示
**需求描述**："网络状态图标（实时更新，无点击交互）"

**可能的设计方案**：
1. **方案A - 简单图标**：WiFi/4G/无网络三种状态
   - 优点：清晰简洁
   - 缺点：信息有限
   
2. **方案B - 详细状态**：显示具体网络类型和信号强度
   - 优点：信息丰富
   - 缺点：可能过于技术化

**建议**：采用方案A，保持界面简洁

**需要确认**：是否需要显示具体的网络类型（3G/4G/5G）？

### 7.4 文件路径显示长度
**需求描述**："文件路径（相对路径，小字显示）"

**可能的设计方案**：
1. **方案A - 完整路径**：显示完整路径，过长时截断
   - 优点：信息完整
   - 缺点：可能占用过多空间
   
2. **方案B - 智能省略**：只显示最后两级目录
   - 优点：节省空间，关键信息突出
   - 缺点：可能丢失部分路径信息

**建议**：采用方案A，使用省略号截断过长路径

**需要确认**：路径显示的具体规则和最大长度限制

### 7.5 歌曲排序功能的实现
**需求描述**："拖拽排序（实时保存）"但又提到"歌曲排序：按加入时间"

**可能的设计方案**：
1. **方案A - 仅支持拖拽**：默认按加入时间，用户可拖拽调整
   - 优点：简单直观
   - 缺点：缺少其他排序选项
   
2. **方案B - 多种排序**：提供排序选项（时间/名称/歌手等）+ 拖拽
   - 优点：功能丰富
   - 缺点：增加复杂度

**建议**：采用方案A，保持简单

**需要确认**：是否需要其他排序方式？拖拽后是否永久改变顺序？

### 7.6 Toast提示的优先级展示
**需求描述**："使用Toast队列按优先级显示"

**需要确认**：
1. 具体的优先级规则是什么？
2. 同时出现多个Toast时的展示策略（队列/替换/并存）？
3. Toast的显示时长是否根据优先级不同？

### 7.7 深色模式支持
**需求描述中未明确提及**

**需要确认**：
1. 是否需要支持iOS系统的深色模式？
2. 如果支持，是否需要独立的开关？

### 7.8 播放模式图标的具体样式
**需求描述**："播放模式（左侧，小图标）：顺序、随机、单曲循环、列表循环"

**需要确认**：
1. 使用系统图标还是自定义图标？
2. 切换方式是点击循环还是弹出选择菜单？

## 8. 设计优先级建议

基于需求文档，建议按以下优先级进行设计实现：

**P0 - 核心功能**（必须实现）
1. 基础播放功能和播放界面
2. 歌单列表和歌曲列表展示
3. 本地缓存管理
4. 基础搜索功能

**P1 - 重要功能**（尽快实现）
1. 批量操作功能
2. 歌单管理（创建、编辑、删除）
3. 文件目录浏览
4. 播放列表管理

**P2 - 增强功能**（后续迭代）
1. 拖拽排序
2. 高级搜索和筛选
3. 详细的流量统计
4. 定时关闭功能

## 9. 下一步行动建议

1. **确认上述歧义点**，特别是影响核心体验的部分
2. **制作高保真原型**，使用Figma或Sketch进行详细设计
3. **进行可用性测试**，验证核心流程的易用性
4. **制定设计规范**，包括颜色、字体、间距等具体数值
5. **考虑适配性**，为未来的iPad版本预留扩展空间
