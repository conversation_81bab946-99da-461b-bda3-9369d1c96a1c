🚨 新发现的逻辑矛盾和技术挑战
1. 后台转码的实现复杂度
您选择"后台自动转码为MP3/AAC"，这带来新问题：
问题：iOS不支持实时转码，需要预先转换
影响：需要额外存储空间存放转码后的文件
--后台播放时跳过不支持的格式，提示用户

2. OSS文件检测策略的盲区
您选择"只在手动更新或下载前检测"：
问题场景：用户播放云端歌曲，但文件已被删除，如何处理？
--下载失败时自动标记该文件为"不可用"状态

3. 播放恢复与"不保存进度"的冲突
您要求"恢复播放列表+播放的歌曲"但"不恢复进度"：
问题：用户期望是什么？从头播放还是暂停状态？
--恢复到暂停状态，位置在0:00

4. 批量删除的具体范围
您确认支持批量删除，但需要明确：
从歌单中批量移除歌曲？✓
--是的
从本地缓存批量删除文件？
--是的
从收藏中批量取消收藏？
--不需要
删除自建歌单本身？
--没看明白

5. Dynamic Island适配与iOS 15.5的矛盾
问题：Dynamic Island需要iOS 16.1+，但您的目标是iOS 15.5
--只需要留白，暂时不用考虑灵动岛的使用

🔍 用户体验的细节问题
1. 播放统计数据的展示位置
您需要记录播放次数、时长等，但在哪里展示？
--咱不用于展示，将来用于推荐或排序展示

2. "最近播放"的定义和排序
问题：是按最后播放时间排序，还是播放频率？
--最后播放时间排序
数量限制：显示最近多少首？50首？100首？
--200首
更新时机：播放超过30秒才算？
--只要播放就算

3. 歌单内拖拽排序的保存时机
问题：是实时保存还是退出时保存？
--实时保存
冲突处理：如果同时在多设备编辑同一歌单？
--覆盖

4. 三个点菜单的完整功能
您提到"右上角三个点"，具体包含哪些选项：
重命名 ✓
删除 ✓
--其他暂不需要

5. 搜索结果的操作限制
搜索View支持批量操作，但是否支持：
在搜索结果中直接收藏/取消收藏？
--支持
从搜索结果删除歌曲？
--支持
查看歌曲详情？
--支持

🎯 技术实现的关键决策
1. 文件下载的并发控制
问题：同时下载多少个文件？
--都是1个并发就可以了

2. 缓存文件的完整性校验
问题：如何确保缓存的文件没有损坏？
--存储文件MD5，播放前校验

3. 播放器状态的持久化粒度
需要保存的状态包括：
当前播放列表 ✓
--需要
当前播放歌曲 ✓
--需要
播放模式
--需要
音量？
--不需要
随机播放的种子？
--不需要

4. 目录扫描的性能优化
问题：深层目录结构扫描可能很慢
--异步加载 + 增量更新

🔒 边界情况处理
1. 存储空间不足的处理
下载时空间不足
--停止下载+按照原清理逻辑清理
转码时空间不足
--不转码了
Core Data数据库过大
--控制缓存大小
缓存有几个选择：不缓存、100M、500M、800M、1G、2G、4G、8G

2. 异常文件的处理
0字节的音频文件
--跳过
损坏的音频文件
--跳过
不支持的音频编码
--跳过

3. 并发操作冲突
正在播放的歌曲被删除
--下一首
正在下载的歌曲被移出歌单
--播放列表保持不变，播放列表与歌单只是在从歌单加入播放列表时有关系，后来就没关系了
正在上传统计数据时切换账号
--停止上传，切换账号时一切重新操作，下次如果读取出错则重置

4. 网络切换的处理
WiFi切换到移动网络时，正在下载的任务
--下载完成当前一首歌，其他的重新规划下载任务
飞行模式切换
--暂停下载，等到恢复时继续
VPN连接变化
--照旧
