# 阿拉摩音乐 iOS 应用设计文档

## 名词定义

### 核心概念
- **歌曲（Song）**：指单个音乐文件实体，包含标题、艺术家、专辑等元数据信息
- **歌单（SongList）**：用户管理歌曲的集合，包括系统歌单和自建歌单两类
  - **系统歌单**：应用自动生成的歌单，如"我的收藏"、"最近播放"等
  - **自建歌单**：用户手动创建的歌单，可自定义名称和内容
- **播放列表（Playlist）**：当前正在播放的歌曲队列，与歌单无关，是临时的播放上下文
- **收藏（Favorite）**：用户标记喜欢的歌曲，收藏的歌曲会出现在"我的收藏"系统歌单中
- **本地缓存（Local Cache）**：已下载到设备本地的歌曲文件，支持离线播放
- **云端歌曲（Cloud Song）**：存储在阿里云OSS上的歌曲文件
- **文件夹（Folder）**：OSS上的目录结构，用户可通过文件夹浏览和播放音乐

### 状态标识
- **☁️ 云端状态**：歌曲存在于OSS云端
- **📱 本地状态**：歌曲已缓存到本地
- **❤️ 收藏状态**：歌曲已被用户收藏

## 1. 系统架构设计

### 1.1 整体架构图

```
┌─────────────────────────────────────────────────────────────────┐
│                     Presentation Layer                          │
├─────────────────────────────────────────────────────────────────┤
│  SongListsView  │  PlayerView  │  FolderView  │  SettingView   │
│                 │              │              │                 │
│  SearchView     │  PlaylistView│  ToastView   │  ConfigView    │
├─────────────────────────────────────────────────────────────────┤
│                      ViewModel Layer                            │
├─────────────────────────────────────────────────────────────────┤
│ SongListViewModel │ PlayerViewModel │ FolderViewModel │ Others  │
├─────────────────────────────────────────────────────────────────┤
│                     Domain Layer                                │
├─────────────────────────────────────────────────────────────────┤
│              UseCases               │         Entities           │
│  ┌─────────────────────────────────┐│  ┌───────────────────────┐ │
│  │ PlaySongUseCase                 ││  │ Song                  │ │
│  │ ManagePlaylistUseCase           ││  │ SongList              │ │
│  │ SyncDataUseCase                 ││  │ PlayHistory           │ │
│  │ ManageCacheUseCase              ││  │ AppSettings           │ │
│  │ SearchSongsUseCase              ││  │ CacheInfo             │ │
│  │ ExtractMetadataUseCase          ││  │ Folder                │ │
│  └─────────────────────────────────┘│  └───────────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│                     Data Layer                                  │
├─────────────────────────────────────────────────────────────────┤
│           Managers                  │         Storage           │
│  ┌─────────────────────────────────┐│  ┌───────────────────────┐ │
│  │ AudioPlayerManager              ││  │ Core Data             │ │
│  │ CacheManager                    ││  │ ┌─────────────────────┤ │
│  │ OSSManager                      ││  │ │ Song Entity         │ │
│  │ SyncManager                     ││  │ │ SongList Entity     │ │
│  │ NetworkMonitor                  ││  │ │ PlayHistory Entity  │ │
│  │ ToastManager                    ││  │ │ AppSettings Entity  │ │
│  │ SearchManager                   ││  │ │ CacheInfo Entity    │ │
│  │ SystemPlaylistManager           ││  │ │ Folder Entity       │ │
│  │ AudioMetadataExtractor          ││  │ └─────────────────────┤ │
│  │ MediaPlayerManager              ││  │ Keychain Storage      │ │
│  │ LiveActivityManager             ││  │ UserDefaults          │ │
│  │ TrafficMonitor                  ││  └───────────────────────┘ │
│  │ TimerManager                    │└─────────────────────────────┘
│  └─────────────────────────────────┘
└─────────────────────────────────────────────────────────────────┘
```

### 1.2 模块职责矩阵

| 模块类别 | 模块名称 | 主要职责 | 依赖模块 | 被依赖模块 |
|----------|----------|----------|----------|------------|
| **Presentation** | SongListsView | 歌单列表显示、用户交互 | SongListViewModel | - |
| | PlayerView | 播放界面、控制播放 | PlayerViewModel | - |
| | FolderView | 目录浏览、文件管理 | FolderViewModel | - |
| | SettingView | 设置配置、状态展示 | SettingViewModel | - |
| **ViewModel** | SongListViewModel | 歌单业务逻辑 | PlaySongUseCase, ManagePlaylistUseCase | SongListsView |
| | PlayerViewModel | 播放控制逻辑 | AudioPlayerManager | PlayerView |
| | FolderViewModel | 目录管理逻辑 | OSSManager, FolderManager | FolderView |
| **Domain** | PlaySongUseCase | 播放歌曲业务流程 | AudioPlayerManager, CacheManager | ViewModels |
| | ManagePlaylistUseCase | 歌单管理业务 | SongRepository, SongListRepository | ViewModels |
| | SyncDataUseCase | 数据同步业务 | SyncManager, OSSManager | Background |
| **Data** | AudioPlayerManager | 音频播放控制 | AVFoundation | UseCases |
| | CacheManager | 缓存管理、LRU清理 | FileManager, Core Data | UseCases |
| | OSSManager | 云端文件管理 | OSS SDK | UseCases |
| | SyncManager | 数据同步管理 | OSSManager, Core Data | UseCases |
| | TrafficMonitor | 流量统计监控 | URLSession, UserDefaults | CacheManager |
| | TimerManager | 定时关闭管理 | Timer, BackgroundTask | AudioPlayerManager |

## 2. 数据架构设计

### 2.1 实体关系图 (ERD)

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│      Song       │    │   SongList      │    │  PlayHistory    │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ id (PK)         │    │ id (PK)         │    │ id (PK)         │
│ title           │◄──┐│ name            │    │ playedAt        │
│ artist          │   ││ type            │    │ playDuration    │
│ album           │   ││ coverImageData  │    │ sourceType      │
│ duration        │   ││ songCount       │    │ sourceId        │
│ filePath        │   ││ createdAt       │    │ songId (FK)     │
│ fileName        │   ││ updatedAt       │    └─────────────────┘
│ fileSize        │   ││ sortOrder       │             │
│ format          │   ││ systemType      │             │
│ isLocal         │   ││ songCount       │             │
│ localPath       │   ││ subfolderCount  │             │
│ albumArtwork    │   ││ addedAt         │             │
│ createdAt       │   ││ updatedAt       │             │
│ updatedAt       │   ││ orderIndex      │             │
│ playCount       │   ││ addedAt         │             │
│ lastPlayedAt    │   ││ addedAt         │             │
│ totalPlayTime   │   ││ addedAt         │             │
│ isFavorite      │   ││ addedAt         │             │
│ isAvailable     │   ││ addedAt         │             │
└─────────────────┘   ││ addedAt         │             │
         │            ││ addedAt         │             │
         │ 1:1        ││ addedAt         │             │
         │            └─────────────┘             │
┌─────────────────┐                                    │
│   CacheInfo     │                                    │
├─────────────────┤                                    │
│ filePath (PK)   │                                    │
│ localPath       │                                    │
│ fileSize        │                                    │
│ cachedAt        │                                    │
│ lastAccessedAt  │                                    │
│ downloadProgress│                                    │
│ isDownloading   │                                    │
│ songId (FK)     │────────────────────────────────────┘
└─────────────────┘

┌─────────────────┐    ┌─────────────────┐
│   AppSettings   │    │     Folder      │
├─────────────────┤    ├─────────────────┤
│ key (PK)        │    │ id (PK)         │
│ value           │    │ name            │
│ type            │    │ path            │
│ updatedAt       │    │ parentPath      │
└─────────────────┘    │ songCount       │
                       │ subfolderCount  │
                       │ updatedAt       │
                       └─────────────────┘
```

### 2.2 Core Data数据库表设计

#### 2.2.1 Song表（歌曲信息）
| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | String | Primary Key | 系统生成的UUID |
| title | String | NOT NULL | 歌曲标题 |
| artist | String | - | 艺术家名称 |
| album | String | - | 专辑名称 |
| duration | Double | DEFAULT 0 | 时长（秒） |
| filePath | String | NOT NULL, UNIQUE | OSS文件路径 |
| fileName | String | NOT NULL | 文件名 |
| fileSize | Int64 | DEFAULT 0 | 文件大小（字节） |
| format | String | - | 文件格式(mp3/m4a/flac/wav) |
| isLocal | Boolean | DEFAULT false | 是否已缓存到本地 |
| localPath | String | - | 本地缓存路径 |
| albumArtwork | Binary | - | 专辑封面数据 |
| createdAt | Date | NOT NULL | 创建时间 |
| updatedAt | Date | NOT NULL | 更新时间 |
| playCount | Int32 | DEFAULT 0 | 播放次数 |
| lastPlayedAt | Date | - | 最后播放时间 |
| totalPlayTime | Double | DEFAULT 0 | 累计播放时长（秒） |
| isFavorite | Boolean | DEFAULT false | 是否收藏 |
| isAvailable | Boolean | DEFAULT true | 文件是否可用 |

索引：
- idx_song_title (title)
- idx_song_artist (artist)
- idx_song_lastPlayedAt (lastPlayedAt)
- idx_song_filePath (filePath)

#### 2.2.2 SongList表（歌单信息）
| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | String | Primary Key | 系统生成的UUID |
| name | String | NOT NULL | 歌单名称 |
| type | String | NOT NULL | 类型(system/custom) |
| systemType | String | - | 系统歌单类型(favorites/recent/local/all/unplayed) |
| coverImageData | Binary | - | 封面图片 |
| songCount | Int32 | DEFAULT 0 | 歌曲数量 |
| createdAt | Date | NOT NULL | 创建时间 |
| updatedAt | Date | NOT NULL | 更新时间 |
| sortOrder | Int32 | DEFAULT 0 | 排序顺序 |

索引：
- idx_songlist_type (type)
- idx_songlist_systemType (systemType)

#### 2.2.3 SongListItem表（歌单歌曲关联）
| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | String | Primary Key | 系统生成的UUID |
| songListId | String | Foreign Key | 歌单ID |
| songId | String | Foreign Key | 歌曲ID |
| orderIndex | Int32 | NOT NULL | 在歌单中的顺序 |
| addedAt | Date | NOT NULL | 添加时间 |

索引：
- idx_songlistitem_composite (songListId, songId) UNIQUE
- idx_songlistitem_order (songListId, orderIndex)

#### 2.2.4 PlayHistory表（播放历史）
| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | String | Primary Key | 系统生成的UUID |
| songId | String | Foreign Key | 歌曲ID |
| playedAt | Date | NOT NULL | 播放时间 |
| playDuration | Double | DEFAULT 0 | 播放时长（秒） |
| sourceType | String | - | 播放来源(songlist/folder/search) |
| sourceId | String | - | 来源ID |

索引：
- idx_playhistory_playedAt (playedAt)
- idx_playhistory_songId (songId)

#### 2.2.5 CacheInfo表（缓存信息）
| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| songId | String | Primary Key, Foreign Key | 歌曲ID |
| filePath | String | NOT NULL | OSS文件路径 |
| localPath | String | NOT NULL | 本地缓存路径 |
| fileSize | Int64 | NOT NULL | 文件大小（字节） |
| cachedAt | Date | NOT NULL | 缓存时间 |
| lastAccessedAt | Date | NOT NULL | 最后访问时间 |
| downloadProgress | Float | DEFAULT 0 | 下载进度(0-1) |
| isDownloading | Boolean | DEFAULT false | 是否正在下载 |

索引：
- idx_cacheinfo_lastAccessedAt (lastAccessedAt)

#### 2.2.6 AppSettings表（应用设置）
| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| key | String | Primary Key | 设置键名 |
| value | String | NOT NULL | 设置值 |
| type | String | NOT NULL | 值类型(string/int/bool/json) |
| updatedAt | Date | NOT NULL | 更新时间 |

### 2.3 数据流向图

```
用户操作 → ViewModel → UseCase → Manager → Storage
    ↑                                        ↓
用户界面 ← ViewModel ← UseCase ← Manager ← Storage

具体示例：播放歌曲数据流
┌─────────────┐   播放请求    ┌─────────────┐   执行播放    ┌─────────────┐
│ PlayerView  │──────────────▶│PlayerViewModel│──────────────▶│PlaySongUseCase│
└─────────────┘               └─────────────┘               └─────────────┘
       ▲                             ▲                             │
       │                             │                             ▼
    播放状态                       播放状态更新               ┌─────────────┐
       │                             │                       │AudioPlayerM │
       └─────────────────────────────┘                       └─────────────┘
                                                                     │
                                                                播放控制
                                                                     ▼
                                                             ┌─────────────┐
                                                             │AVFoundation │
                                                             └─────────────┘
```
## 3. 数据管理设计

### 3.1 Core Data 管理策略

#### 3.1.1 数据库操作策略
| 操作类型 | 执行线程 | 上下文类型 | 同步方式 | 应用场景 |
|----------|----------|------------|----------|----------|
| 读取操作 | 主线程 | ViewContext | 同步 | UI数据绑定 |
| 批量插入 | 后台线程 | BackgroundContext | 异步 | OSS文件索引 |
| 单个更新 | 主线程 | ViewContext | 同步 | 用户操作反馈 |
| 播放统计 | 后台线程 | BackgroundContext | 异步 | 播放记录更新 |
| 搜索查询 | 后台线程 | BackgroundContext | 异步 | 搜索结果 |

#### 3.1.2 数据一致性保证
```
数据一致性策略
├── 主从上下文同步
│   ├── ViewContext (主线程，UI绑定)
│   └── BackgroundContext (后台线程，数据处理)
├── 变更通知机制
│   ├── NSManagedObjectContextDidSave
│   └── 跨上下文变更合并
└── 事务控制
    ├── 批量操作事务化
    └── 失败回滚机制
```

#### 3.1.3 性能优化策略
```
Core Data 性能优化
├── 查询优化
│   ├── 建立索引 (title, artist, lastPlayedAt)
│   ├── 分页加载 (fetchLimit + fetchOffset)
│   ├── 谓词优化 (复合条件查询)
├── 内存管理
│   ├── 故障对象 (Fault) 机制
│   ├── 批量插入 (NSBatchInsertRequest)
│   └── 定期清理未使用对象
└── 缓存策略
    ├── NSFetchedResultsController
    └── 查询结果缓存
```

### 3.2 数据同步机制

#### 3.2.1 同步策略矩阵
| 数据类型 | 同步频率 | 冲突解决 | 失败重试 | 网络要求 |
|----------|----------|----------|----------|----------|
| 歌单信息 | 定时同步(5分钟间隔) | 后写入覆盖 | 3次，指数退避 | 任意网络 |
| 收藏状态 | 定时同步(5分钟间隔) | 后写入覆盖 | 3次，指数退避 | 任意网络 |
| 应用设置 | 定时同步(5分钟间隔) | 后写入覆盖 | 3次，指数退避 | 任意网络 |
| 播放历史 | 延迟同步(5分钟) | 追加合并 | 5次，定期重试 | WiFi优先 |
| 播放统计 | 延迟同步(5分钟) | 累加合并 | 5次，定期重试 | WiFi优先 |

#### 3.2.2 同步状态流转
```mermaid
stateDiagram-v2
    [*] --> 空闲
    空闲 --> 准备同步 : 定时器/手动触发
    准备同步 --> 检查网络 : 开始同步
    检查网络 --> 上传数据 : 网络可用
    检查网络 --> 等待网络 : 网络不可用
    等待网络 --> 检查网络 : 网络恢复
    上传数据 --> 下载数据 : 上传成功
    上传数据 --> 重试上传 : 上传失败
    重试上传 --> 上传数据 : 重试
    重试上传 --> 空闲 : 超过重试次数
    下载数据 --> 合并数据 : 下载成功
    下载数据 --> 重试下载 : 下载失败
    重试下载 --> 下载数据 : 重试
    重试下载 --> 空闲 : 超过重试次数
    合并数据 --> 空闲 : 同步完成
```

## 4. 配置管理设计

### 4.1 配置分层架构

```
配置管理架构
├── 应用级配置 (AppSettings)
│   ├── 缓存大小限制
│   ├── 预加载策略
│   ├── 网络超时设置
│   └── 播放模式偏好
├── 用户级配置 (UserDefaults)
│   ├── UI偏好设置
│   ├── 流量统计数据
│   ├── 最后同步时间
│   └── 播放历史缓存
└── 安全配置 (Keychain)
    ├── OSS访问密钥
    ├── 加密证书
    └── 设备标识符
```

### 4.2 OSS配置管理

#### 4.2.1 配置存储策略
| 配置项 | 存储位置 | 加密方式 | 验证策略 | 更新时机 |
|--------|----------|----------|----------|----------|
| Endpoint | Keychain | AES-256 | 连接测试 | 立即生效 |
| AccessKeyId | Keychain | AES-256 | 权限验证 | 立即生效 |
| AccessKeySecret | Keychain | AES-256 | 签名验证 | 立即生效 |
| BucketName | Keychain | AES-256 | 存在性检查 | 立即生效 |
| 连接状态 | 内存 | 无 | 实时检测 | 30秒更新 |

#### 4.2.2 配置验证流程
```mermaid
flowchart TD
    A[用户输入配置] --> B[格式验证]
    B --> C{格式正确?}
    C -->|否| D[显示错误提示]
    C -->|是| E[加密存储]
    E --> F[测试连接]
    F --> G{连接成功?}
    G -->|否| H[显示连接失败]
    G -->|是| I[保存配置]
    I --> J[更新连接状态]
    H --> K[保留旧配置]
    D --> A
    K --> A
```

### 4.3 设置持久化策略

#### 4.3.1 设置类型分类
```
设置分类管理
├── 即时生效设置
│   ├── 播放模式 → AudioPlayerManager
│   ├── 音量控制 → AVAudioSession
│   └── 界面主题 → UI Layer
├── 重启生效设置
│   ├── 缓存目录位置
│   ├── 数据库配置
│   └── 网络配置
└── 后台生效设置
    ├── 同步频率 → SyncManager
    ├── 预加载策略 → CacheManager
    └── 清理策略 → CacheManager
```

## 5. 存储管理设计

### 5.1 三层存储架构

```
存储架构设计
├── 内存层 (Memory)
│   ├── 图片缓存 (50MB LRU)
│   ├── 元数据缓存 (Core Data Fault)
│   ├── 播放队列 (Array)
│   └── 网络状态 (Published)
├── 本地层 (Local)
│   ├── Core Data (结构化数据)
│   ├── 文件缓存 (歌曲文件)
│   ├── UserDefaults (用户偏好)
│   └── Keychain (敏感配置)
└── 云端层 (OSS)
    ├── 歌曲文件存储
    ├── 同步数据存储
    └── 元数据备份
```

### 5.2 本地存储管理

#### 5.2.1 目录结构设计
```
应用存储目录结构
Documents/
├── CoreData/
│   └── AlaMusic.sqlite
├── Settings/
│   └── UserPreferences.plist
└── Logs/
    └── error.log

Caches/
├── AudioCache/
│   └── Songs/
│       └── [歌曲UUID].[格式]  // 使用Core Data生成的唯一UUID作为文件名
├── ImageCache/
│   └── Artwork/
│       └── [歌曲UUID].jpg     // 专辑封面使用相同的UUID
└── TempDownloads/
    └── [临时下载文件]

Library/
├── Preferences/
│   └── [应用包名].plist
└── Keychains/
    └── [OSS配置]
```

注：使用UUID作为文件名的优势：
- 避免文件路径特殊字符问题
- 确保文件名唯一性
- 便于文件管理和查找

#### 5.2.2 缓存管理策略
| 缓存类型 | 大小限制 | 清理策略 | 清理时机 | 保护策略 |
|----------|----------|----------|----------|----------|
| 歌曲文件 | 用户配置 | 加权LRU | 空间不足时 | 播放队列保护 |
| 专辑封面 | 50MB | 标准LRU | 内存警告时 | 当前显示保护 |
| 元数据 | 无限制 | Core Data管理 | 应用清理时 | 关键数据保护 |
| 临时文件 | 100MB | 年龄清理 | 启动时 | 下载中保护 |

### 5.3 OSS存储管理

#### 5.3.1 云端目录规划
```
OSS Bucket 结构
bucket-name/
├── alamusic/                    # 音乐文件根目录（用户可自由组织）
│   ├── [用户自定义目录结构]    # 用户可创建任意目录层级
│   │   └── [音乐文件]
│   └── .alamusic.data/         # 应用数据目录（系统保留）
│       ├── playlists.json      # 歌单信息
│       ├── favorites.json      # 收藏列表
│       ├── settings.json       # 应用设置
│       ├── play_history.json   # 播放历史
│       └── play_stats.json     # 播放统计
```

注：应用不对用户的音乐文件组织方式做任何限制，用户可以：
- 创建任意层级的目录结构
- 按照自己的喜好组织音乐文件
- 在任意目录下放置音乐文件

#### 5.3.2 文件操作策略
| 操作类型 | 并发数 | 超时时间 | 重试策略 | 校验方式 |
|----------|--------|----------|----------|----------|
| 文件下载 | 3个 | 60秒 | 3次，指数退避 | MD5校验 |
| 文件上传 | 1个 | 120秒 | 5次，线性退避 | MD5校验 |
| 列表查询 | 1个 | 30秒 | 3次，固定间隔 | 响应格式校验 |
| 删除操作 | 1个 | 30秒 | 2次，固定间隔 | 状态码校验 |

### 5.4 内存存储管理

#### 5.4.1 内存缓存策略
```
内存缓存管理
├── 图片缓存 (NSCache)
│   ├── 最大数量: 100张
│   ├── 最大内存: 50MB
│   ├── 清理策略: LRU + 内存压力
│   └── 生命周期: 应用级
├── 数据缓存 (Core Data)
│   ├── 对象上下文缓存
│   ├── 故障对象机制
│   ├── 批量操作缓存
│   └── 生命周期: 上下文级
└── 临时缓存 (Dictionary)
    ├── 网络请求缓存
    ├── 计算结果缓存
    ├── 状态快照缓存
    └── 生命周期: 会话级
```

#### 5.4.2 内存压力处理
```mermaid
flowchart TD
    A[收到内存警告] --> B[暂停下载任务]
    B --> C[清理图片缓存]
    C --> D[释放非关键对象]
    D --> E[压缩播放队列]
    E --> F[通知各模块清理]
    F --> G[记录内存状态]
    G --> H[继续监控]
``` 

## 6. 异步/同步设计

### 6.1 线程管理策略

#### 6.1.1 GCD队列分工矩阵
| 队列类型 | 职责范围 | 执行任务 | 队列属性 | 生命周期 |
|----------|----------|----------|----------|----------|
| 主队列 | UI操作、用户交互 | 界面更新、事件响应 | 串行 | 应用级 |
| 音频队列 | 音频播放控制 | AVPlayer操作、进度更新 | 串行 | 播放会话级 |
| 下载队列 | 文件下载管理 | OSS下载、缓存写入 | 并发（限制3） | 任务级 |
| 数据队列 | 数据库操作 | Core Data读写、索引更新 | 串行 | 应用级 |
| 同步队列 | 云端数据同步 | 数据上传下载、冲突解决 | 串行 | 应用级 |

#### 6.1.2 GCD队列通信设计
```
GCD队列架构
├── 主队列 (DispatchQueue.main)
│   ├── 接收用户输入
│   ├── 更新界面状态
│   └── 分发后台任务
├── 自定义队列
│   ├── 串行队列
│   │   ├── audioQueue (音频控制)
│   │   ├── dataQueue (数据库操作)
│   │   └── syncQueue (数据同步)
│   └── 并发队列
│       ├── downloadQueue (文件下载，maxConcurrent: 3)
│       ├── imageQueue (图片处理)
│       └── searchQueue (搜索处理)
└── 通信机制
    ├── DispatchQueue.main.async (回到主线程)
    ├── Combine Publishers (状态传播)
    ├── NotificationCenter (跨模块通知)
    └── 回调闭包 (任务完成通知)
```

### 6.2 异步操作设计

#### 6.2.1 异步任务分类
| 任务类型 | 执行方式 | 超时设置 | 错误处理 | 进度反馈 |
|----------|----------|----------|----------|----------|
| 文件下载 | URLSession异步 | 60秒 | 重试3次 | 进度条 |
| 数据库操作 | Core Data异步 | 30秒 | 事务回滚 | 无 |
| 云端同步 | Timer + async/await | 120秒 | 队列重试 | Toast提示 |
| 元数据提取 | AVAsset异步 | 10秒 | 降级处理 | 加载指示器 |
| 搜索查询 | 防抖 + 异步 | 5秒 | 取消前次 | 搜索结果更新 |

#### 6.2.2 异步操作流程
```mermaid
sequenceDiagram
    participant UI as 主线程(UI)
    participant BG as 后台队列
    participant CD as Core Data
    participant OSS as OSS服务
    
    UI->>BG: 启动异步任务
    BG->>CD: 数据库操作 (异步)
    CD-->>BG: 操作结果
    BG->>OSS: 网络请求 (异步)
    OSS-->>BG: 响应数据
    BG->>UI: 更新结果 (主线程)
    UI->>UI: 界面更新
```

### 6.3 同步操作设计

#### 6.3.1 同步任务界定
| 操作类型 | 同步原因 | 执行位置 | 超时限制 | 异常处理 |
|----------|----------|----------|----------|----------|
| 用户输入响应 | UI实时反馈 | 主线程 | 100ms | 降级显示 |
| 播放状态读取 | 状态一致性 | 主线程 | 10ms | 缓存状态 |
| 配置参数读取 | 数据完整性 | 任意线程 | 1ms | 默认值 |
| 关键错误处理 | 用户安全 | 主线程 | 即时 | 强制处理 |

#### 6.3.2 同步策略保障
```
同步操作保障机制
├── 性能保障
│   ├── 操作时间限制 (< 100ms)
│   ├── 缓存机制减少计算
│   └── 预计算关键数据
├── 一致性保障
│   ├── 原子操作保证
│   ├── 锁机制防冲突
│   └── 状态快照机制
└── 容错保障
    ├── 超时自动降级
    ├── 异常状态恢复
    └── 默认值兜底
```

### 6.4 并发控制设计

#### 6.4.1 并发限制策略
```
并发控制架构
├── 下载并发控制
│   ├── 最大并发数: 3个
│   ├── 队列管理: FIFO + 优先级
│   └── 网络切换处理: 动态调整
├── 数据库并发控制
│   ├── 读操作: 无限制并发
│   ├── 写操作: 串行化执行
│   └── 批量操作: 事务保护
└── 计算并发控制
    ├── CPU密集型: 核心数限制
    ├── IO密集型: 适度并发
    └── 内存敏感: 动态调整
```

#### 6.4.2 死锁预防策略
```
死锁预防原则
├── 锁的使用范围
│   ├── 锁必须在当前函数内获取和释放
│   ├── 避免跨函数传递锁
│   ├── 使用defer确保锁释放
│   └── 优先使用值类型避免共享状态
├── 资源访问顺序
│   ├── 定义全局资源访问顺序
│   ├── 始终按照相同顺序获取多个锁
│   ├── 避免嵌套锁
│   └── 使用超时机制防止永久等待
└── 并发设计模式
    ├── Actor模式隔离状态
    ├── 串行队列保护共享资源
    ├── 使用不可变数据结构
    └── 优先使用高级并发API
```

示例代码：
```swift
// 正确：锁在函数内获取和释放
func updateCache(song: Song) {
    cacheLock.lock()
    defer { cacheLock.unlock() }
    // 更新缓存操作
}

// 错误：跨函数使用锁
func beginUpdate() {
    cacheLock.lock() // 危险：锁可能永远不会释放
}

func endUpdate() {
    cacheLock.unlock() // 危险：可能忘记调用
}
```

## 7. 核心流程设计

### 7.1 应用启动时序图

```mermaid
sequenceDiagram
    participant App as AlamusicApp
    participant State as AppStateManager
    participant CD as CoreData
    participant OSS as OSSManager
    participant Audio as AudioPlayerManager
    participant UI as ContentView

    App->>State: 启动应用
    State->>CD: 初始化Core Data (异步)
    CD-->>State: 数据库就绪
    State->>Audio: 配置音频会话 (同步)
    Audio-->>State: 音频会话配置完成
    State->>OSS: 尝试连接OSS (异步)
    OSS-->>State: 连接状态
    State->>State: 恢复播放状态 (异步)
    State->>UI: 应用初始化完成
    UI->>UI: 显示主界面
```

### 7.2 歌曲播放时序图

```mermaid
sequenceDiagram
    participant User as 用户
    participant View as PlayerView
    participant VM as PlayerViewModel
    participant UC as PlaySongUseCase
    participant APM as AudioPlayerManager
    participant CM as CacheManager
    participant SR as SongRepository

    User->>View: 点击播放歌曲
    View->>VM: 播放请求(song, playlist)
    VM->>UC: 执行播放(song, playlist) (异步)
    UC->>SR: 标记为已播放 (异步)
    SR-->>UC: 更新完成
    
    alt 歌曲未缓存
        UC->>CM: 请求下载歌曲 (异步)
        CM-->>UC: 下载进度/完成
    end
    
    UC->>APM: 开始播放 (同步)
    APM-->>UC: 播放状态更新
    UC->>CM: 预加载下一首 (异步)
    UC-->>VM: 播放开始
    VM-->>View: 更新UI状态 (主线程)
```

### 7.3 空状态UI设计

#### 7.3.1 播放界面空状态
```
空状态播放界面布局
├── 顶部栏
│   ├── 返回按钮 (可用)
│   ├── "播放器"标题
│   └── 网络状态图标
├── 中心内容
│   ├── 音符图标 (SF Symbol: music.note)
│   ├── 提示文字: "暂无播放歌曲"
│   └── 副标题: "从歌单或目录选择歌曲开始播放"
└── 底部操作栏
    ├── 收藏按钮 (禁用状态)
    ├── 添加到歌单 (禁用状态)
    └── 播放列表 (可用但为空)
```

#### 7.3.2 其他空状态设计
| 场景 | 图标 | 主文字 | 副文字 | 操作按钮 |
|------|------|--------|--------|----------|
| 空歌单 | music.note.list | 歌单还没有歌曲 | 从目录或搜索添加歌曲 | 添加歌曲 |
| 搜索无结果 | magnifyingglass | 没有找到相关歌曲 | 换个关键词试试 | - |
| 网络错误 | wifi.slash | 网络连接失败 | 请检查网络设置 | 重试 |
| OSS未配置 | icloud.slash | 尚未配置云存储 | 前往设置配置阿里云OSS | 去设置 |
| 无缓存歌曲 | arrow.down.circle | 暂无离线歌曲 | 连接网络后可下载歌曲 | - |

#### 7.3.3 空状态交互设计
```mermaid
flowchart TD
    A[显示空状态] --> B{用户操作}
    B -->|点击操作按钮| C[执行对应操作]
    B -->|下拉刷新| D[尝试重新加载]
    B -->|返回| E[回到上级界面]
    C --> F[更新界面状态]
    D --> G{加载成功?}
    G -->|是| F
    G -->|否| A
```

### 7.4 缓存管理流程图

```mermaid
flowchart TD
    A[检查缓存空间] --> B{空间充足?}
    B -->|是| C[直接缓存新文件]
    B -->|否| D[执行LRU清理 (异步)]
    
    D --> E[计算清理分数]
    E --> F[分数 = 时间因子×0.4 + 播放频率×0.3 + 文件大小×0.2 + 保护因子×0.1]
    F --> G[按分数排序]
    G --> H[删除高分文件]
    H --> I{空间足够?}
    I -->|否| H
    I -->|是| C
    C --> J[更新缓存记录 (异步)]
    
    K[保护策略] --> L[当前播放队列: -1000分]
    K --> M[正在下载: -500分]
    K --> N[收藏歌曲: -100分]
```

### 7.5 数据同步状态图

```mermaid
stateDiagram-v2
    [*] --> 空闲状态
    空闲状态 --> 实时同步 : 定时器触发(5分钟,异步)
    空闲状态 --> 延迟同步 : 定时器触发(5分钟,异步)
    空闲状态 --> 手动同步 : 用户触发(异步)
    
    实时同步 --> 上传歌单数据
    实时同步 --> 上传收藏数据
    实时同步 --> 上传设置数据
    
    延迟同步 --> 上传播放历史
    延迟同步 --> 上传播放统计
    
    手动同步 --> 完整上传
    完整上传 --> 完整下载
    
    上传歌单数据 --> 检查冲突
    上传收藏数据 --> 检查冲突
    上传设置数据 --> 检查冲突
    上传播放历史 --> 检查冲突
    上传播放统计 --> 检查冲突
    完整下载 --> 检查冲突
    
    检查冲突 --> 后写入覆盖 : 云端更新
    检查冲突 --> 保持本地 : 本地更新
    
    后写入覆盖 --> 空闲状态
    保持本地 --> 空闲状态
```

### 7.6 文件夹播放流程设计

#### 7.6.1 文件夹播放时序图
```mermaid
sequenceDiagram
    participant User as 用户
    participant FV as FolderView
    participant FVM as FolderViewModel
    participant OSS as OSSManager
    participant APM as AudioPlayerManager
    participant CM as CacheManager

    User->>FV: 点击播放文件夹
    FV->>FVM: 播放文件夹请求
    FVM->>OSS: 递归扫描文件夹 (异步)
    
    loop 遍历所有子目录
        OSS->>OSS: 获取子目录文件列表
        OSS->>OSS: 过滤音乐文件(mp3/m4a/flac/wav)
    end
    
    OSS-->>FVM: 返回所有音乐文件列表
    FVM->>FVM: 按路径/文件名排序
    FVM->>APM: 播放文件列表
    APM-->>FVM: 播放开始
    FVM-->>FV: 更新UI状态
    
    Note over APM,CM: 后续播放流程同歌曲播放
```

#### 7.6.2 文件夹扫描算法
```
算法: 递归文件夹扫描
输入: folderPath (文件夹路径)
输出: [Song] (该文件夹及子文件夹下的所有歌曲)

1. 初始化结果数组 songs = []
2. 获取当前文件夹内容列表
3. 对每个项目:
   如果是文件:
     - 检查是否为支持的音乐格式
     - 是则添加到songs数组
   如果是文件夹:
     - 递归调用扫描该子文件夹
     - 将结果合并到songs数组
4. 返回songs数组

排序规则:
- 按完整路径+文件名的字母顺序排序
- 确保同一文件夹内的文件连续播放
- 子文件夹的文件在父文件夹文件之后

时间复杂度: O(n log n) - n为文件总数（含排序）
空间复杂度: O(d) - d为最大目录深度
并发优化: 使用并发队列并行扫描子目录
```

#### 7.6.3 文件夹播放状态管理
```
文件夹播放上下文
├── 播放源信息
│   ├── sourceType: "folder"
│   ├── sourcePath: 文件夹路径
│   └── sourceId: 文件夹标识
├── 播放列表集成
│   ├── 扫描结果直接加入当前播放列表
│   ├── 与其他来源的歌曲同等对待
│   └── 支持所有播放模式（顺序/随机/循环）
└── 缓存策略
    ├── 缓存扫描结果5分钟
    ├── 文件变化时自动失效
    └── 避免重复扫描提升性能
```

## 8. 接口设计规范

### 8.1 Repository接口定义

#### 8.1.1 SongRepository
| 方法名 | 输入参数 | 返回类型 | 执行方式 | 错误处理 |
|--------|----------|----------|----------|----------|
| getAllSongs | - | Publisher<[Song], Error> | 异步 | CoreDataError |
| getSong | id: String | Publisher<Song?, Error> | 异步 | NotFoundError |
| searchSongs | query: String | Publisher<[Song], Error> | 异步+防抖 | SearchError |
| addSong | song: Song | Publisher<Void, Error> | 异步 | ValidationError |
| updateSong | song: Song | Publisher<Void, Error> | 异步 | ConcurrencyError |
| deleteSong | song: Song | Publisher<Void, Error> | 异步 | ReferenceError |
| markAsPlayed | song: Song | Publisher<Void, Error> | 异步 | UpdateError |

#### 8.1.2 SongListRepository
| 方法名 | 输入参数 | 返回类型 | 执行方式 | 错误处理 |
|--------|----------|----------|----------|----------|
| getAllSongLists | - | Publisher<[SongList], Error> | 异步 | CoreDataError |
| createSongList | songList: SongList | Publisher<Void, Error> | 异步 | DuplicateError |
| updateSongList | songList: SongList | Publisher<Void, Error> | 异步 | ValidationError |
| deleteSongList | songList: SongList | Publisher<Void, Error> | 异步 | ReferenceError |
| addSong | song: Song, songList: SongList | Publisher<Void, Error> | 异步 | DuplicateError |
| removeSong | song: Song, songList: SongList | Publisher<Void, Error> | 异步 | NotFoundError |
| reorderSongs | in: SongList, from: Int, to: Int | Result<Void, PlaylistError> | 异步 | 排序更新 | 

### 8.2 Manager接口定义

#### 8.2.1 AudioPlayerManager
| 属性/方法 | 类型 | 执行方式 | 功能描述 | 状态依赖 |
|-----------|------|----------|----------|----------|
| isPlaying | @Published Bool | 同步读取 | 播放状态 | - |
| currentTime | @Published TimeInterval | 同步读取 | 当前播放时间 | 播放中 |
| duration | @Published TimeInterval | 同步读取 | 总时长 | 已加载 |
| currentSong | @Published Song? | 同步读取 | 当前歌曲 | - |
| playQueue | @Published [Song] | 同步读取 | 播放队列 | - |
| playMode | @Published PlayMode | 同步读取 | 播放模式 | - |
| play(song:) | (Song) -> Void | 异步执行 | 播放单首歌曲 | 空闲/播放中 |
| play(songs:startIndex:) | ([Song], Int) -> Void | 异步执行 | 播放歌曲列表 | 空闲/播放中 |
| pause() | () -> Void | 同步执行 | 暂停播放 | 播放中 |
| resume() | () -> Void | 同步执行 | 继续播放 | 暂停 |
| next() | () -> Void | 异步执行 | 下一首 | 播放中/暂停 |
| previous() | () -> Void | 异步执行 | 上一首 | 播放中/暂停 |
| seek(to:) | (TimeInterval) -> Void | 同步执行 | 跳转到指定时间 | 播放中/暂停 |

#### 8.2.2 CacheManager
| 属性/方法 | 类型 | 执行方式 | 功能描述 | 限制条件 |
|-----------|------|----------|----------|----------|
| currentCacheSize | @Published Int64 | 同步读取 | 当前缓存大小 | 只读 |
| maxCacheSize | Int64 | 同步读取 | 最大缓存限制 | 可配置 |
| downloadProgress | @Published [String: Float] | 同步读取 | 下载进度字典 | 只读 |
| localPath(for:) | (Song) -> URL | 同步计算 | 获取本地路径 | - |
| downloadSong(_:priority:) | (Song, Priority) -> Void | 异步执行 | 下载歌曲 | 网络连接 |
| removeFromCache(_:) | (Song) -> Bool | 异步执行 | 移除缓存 | 非播放中 |
| cleanupIfNeeded() | () -> Void | 异步执行 | 清理缓存 | 后台执行 |
| preloadNext(songs:currentIndex:isWiFi:) | ([Song], Int, Bool) -> Void | 异步执行 | 预加载下一首 | 网络连接 |

### 8.3 UseCase接口定义

#### 8.3.1 PlaySongUseCase
| 方法名 | 输入参数 | 输出 | 执行方式 | 副作用 |
|--------|----------|------|----------|--------|
| execute | song: Song, playlist: [Song] | Result<Void, PlayError> | 异步 | 更新播放统计、触发缓存 |

#### 8.3.2 ManagePlaylistUseCase
| 方法名 | 输入参数 | 输出 | 执行方式 | 副作用 |
|--------|----------|------|----------|--------|
| createPlaylist | name: String | Result<SongList, PlaylistError> | 异步 | 数据库插入 |
| deletePlaylist | playlist: SongList | Result<Void, PlaylistError> | 异步 | 数据库删除 |
| addSongs | songs: [Song], to: SongList | Result<Void, PlaylistError> | 异步 | 关系表更新 |
| removeSongs | songs: [Song], from: SongList | Result<Void, PlaylistError> | 异步 | 关系表删除 |
| reorderSongs | in: SongList, from: Int, to: Int | Result<Void, PlaylistError> | 异步 | 排序更新 | 

### 8.4 TimerManager接口定义
| 属性/方法 | 类型 | 执行方式 | 功能描述 | 限制条件 |
|-----------|------|----------|----------|----------|
| isActive | @Published Bool | 同步读取 | 定时器激活状态 | 只读 |
| remainingTime | @Published TimeInterval | 同步读取 | 剩余时间（秒） | 只读 |
| selectedDuration | TimeInterval | 同步读写 | 设定时长 | 15/30/60分钟 |
| startTimer(duration:) | (TimeInterval) -> Void | 异步执行 | 启动定时器 | 需要后台权限 |
| cancelTimer() | () -> Void | 同步执行 | 取消定时器 | - |
| handleTimeout() | () -> Void | 异步执行 | 处理超时 | 暂停播放+保存状态 |

#### 8.5 TrafficMonitor接口定义
| 属性/方法 | 类型 | 执行方式 | 功能描述 | 限制条件 |
|-----------|------|----------|----------|----------|
| monthlyTotal | @Published Int64 | 同步读取 | 本月总流量（字节） | 只读 |
| mobileTotal | @Published Int64 | 同步读取 | 本月移动流量（字节） | 只读 |
| currentNetworkType | @Published NetworkType | 同步读取 | 当前网络类型 | 只读 |
| startMonitoring() | () -> Void | 异步执行 | 开始监控 | 应用启动时 |
| recordDownload(bytes:isMobile:) | (Int64, Bool) -> Void | 同步执行 | 记录下载流量 | - |
| resetMonthlyStats() | () -> Void | 异步执行 | 重置月度统计 | 每月1日自动 |
| getFormattedStats() | () -> (String, String) | 同步计算 | 获取格式化统计 | 返回GB/MB格式 |

## 9. 关键算法设计

### 9.1 系统歌单动态生成算法

**输入**: 系统歌单类型 (favorites/recent/local/all/unplayed)  
**输出**: 歌曲列表  
**执行方式**: 异步查询

```
算法: 动态系统歌单生成
1. 根据歌单类型构建Core Data查询谓词：
   - favorites: isFavorite == true
   - recent: lastPlayedAt != nil, 限制200首, 按时间倒序
   - local: isLocal == true
   - all: 无谓词, 全部歌曲
   - unplayed: playCount == 0

2. 设置排序规则：
   - favorites: 按更新时间倒序
   - recent: 按最后播放时间倒序
   - local: 按标题正序
   - all: 按标题正序
   - unplayed: 按创建时间倒序

3. 执行查询并返回结果

时间复杂度: O(n log n) - 基于数据库索引查询
空间复杂度: O(k) - k为结果集大小
缓存策略: NSFetchedResultsController缓存结果
```

### 9.2 加权LRU缓存清理算法

**输入**: 当前缓存歌曲集合, 目标缓存大小  
**输出**: 清理的歌曲列表  
**执行方式**: 异步后台执行

```
算法: 加权LRU缓存清理
1. 对每首歌计算清理分数:
   分数 = 时间因子 × 0.4 + 播放频率因子 × 0.3 + 文件大小因子 × 0.2 + 保护因子 × 0.1
   
   其中:
   - 时间因子 = min(距离最后播放天数, 365) / 365
   - 播放频率因子 = 1 / max(播放次数, 1)
   - 文件大小因子 = min(文件大小(MB), 100) / 100
   - 保护因子 = 根据保护级别的负分

2. 保护策略:
   - 当前播放队列: -10 (高度保护)
   - 正在下载: -5 (中度保护)  
   - 收藏歌曲: -1 (轻度保护)
   - 最近7天播放: -0.5 (轻度保护)

3. 按分数降序排列，优先删除高分歌曲

4. 持续删除直到缓存大小降至目标的80%

时间复杂度: O(n log n) - 排序主导
空间复杂度: O(n) - 分数计算数组
并发安全: 使用串行队列保证线程安全
```

### 9.3 歌曲元数据提取算法

**输入**: 歌曲文件URL (本地/网络)  
**输出**: 歌曲元数据对象  
**执行方式**: 异步AVFoundation

```
算法: 歌曲元数据提取
1. 创建AVAsset对象 (异步)
2. 异步加载基础信息:
   - 时长 (duration)
   - 元数据数组 (metadata)

3. 遍历元数据项，提取:
   - 标题 (commonKeyTitle)
   - 艺术家 (commonKeyArtist)
   - 专辑 (commonKeyAlbumName)
   - 封面 (commonKeyArtwork)
   - 流派 (commonKeyGenre)
   - 年份 (commonKeyCreationDate)

4. 数据清理:
   - 标题为空时使用文件名
   - 验证时长有效性 (> 0)
   - 压缩封面图片 (最大200x200)
   - 处理特殊字符和编码

5. 返回元数据对象

时间复杂度: O(1) - 主要是I/O等待
错误处理: 捕获解析失败，返回基础信息
超时机制: 10秒超时，返回部分信息
```

### 9.4 智能预加载算法

**输入**: 当前播放位置, 播放列表, 网络状态  
**输出**: 预加载任务列表  
**执行方式**: 异步下载

```
算法: 智能预加载
1. 根据网络状态确定预加载数量:
   - WiFi/以太网: 2-3首
   - 移动网络: 1首
   - 弱网络: 0首

2. 根据播放模式确定预加载顺序:
   - 顺序播放: 按列表顺序
   - 随机播放: 随机选择
   - 单曲循环: 仅当前歌曲
   - 列表循环: 考虑循环边界

3. 优先级策略:
   - 下一首: 最高优先级
   - 后续歌曲: 递减优先级
   - 已缓存歌曲: 跳过

4. 动态调整:
   - 网络状态变化时重新评估
   - 播放模式变化时重新计算
   - 播放列表变化时取消当前任务

5. 启动预加载任务 (后台队列)

时间复杂度: O(k) - k为预加载数量
资源控制: 限制最大并发下载数为3
取消机制: 支持任务取消和优先级调整
```

### 9.5 定时关闭管理算法

**输入**: 定时时长(15/30/60分钟)  
**输出**: 定时关闭执行  
**执行方式**: Timer + 后台任务

```
算法: 定时关闭管理
1. 创建定时器:
   - 使用Timer.scheduledTimer
   - 设置时间间隔为选定时长
   - 每秒更新剩余时间UI

2. 后台保活:
   - 注册后台任务(BGTaskScheduler)
   - 设置后台音频播放模式
   - 定期唤醒检查剩余时间

3. 时间到达处理:
   - 暂停当前播放
   - 保存播放状态(歌曲ID、播放列表、播放模式)
   - 取消所有下载任务
   - 发送本地通知提醒用户

4. 状态恢复:
   - 应用重启时检查是否有保存的定时状态
   - 恢复剩余时间继续计时
   - 用户可手动取消定时

时间复杂度: O(1) - 定时器操作
内存占用: O(1) - 仅保存定时状态
后台支持: 使用后台任务确保准确执行
```

### 9.6 流量统计管理算法

**输入**: 下载字节数, 网络类型  
**输出**: 流量统计数据  
**执行方式**: 实时累加 + 定期持久化

```
算法: 流量统计管理
1. 监听下载事件:
   - URLSessionDelegate监听数据传输
   - 区分WiFi/移动网络类型
   - 累加到对应的计数器

2. 数据存储结构:
   struct TrafficStats {
       var monthlyTotal: Int64
       var mobileTotal: Int64
       var lastResetDate: Date
   }

3. 月度重置检查:
   - 每次启动检查当前日期
   - 如果是新月份(date.day == 1 && month != lastMonth):
     * 重置monthlyTotal = 0
     * 重置mobileTotal = 0
     * 更新lastResetDate

4. 持久化策略:
   - 使用UserDefaults存储
   - 每次下载完成后更新
   - 应用进入后台时保存

5. 格式化显示:
   - < 1MB: 显示KB
   - < 1GB: 显示MB(保留1位小数)
   - >= 1GB: 显示GB(保留2位小数)

时间复杂度: O(1) - 累加操作
空间复杂度: O(1) - 固定大小存储
精度保证: 使用Int64避免溢出
```

### 9.7 网络切换处理算法

**输入**: 网络状态变化事件  
**输出**: 缓存策略调整  
**执行方式**: NetworkMonitor + 策略调整

```
算法: 网络切换处理
1. 监听网络状态变化:
   - 使用NWPathMonitor监听
   - 识别WiFi/移动网络/无网络状态
   - 检测网络质量(优秀/一般/差)

2. WiFi到移动网络切换:
   - 正在播放的歌曲继续播放(已缓存或继续下载)
   - 暂停非关键预加载任务
   - 调整预加载策略: 2-3首 → 1首
   - 保持当前下载任务完成

3. 移动网络到WiFi切换:
   - 恢复预加载策略: 1首 → 2-3首
   - 启动延迟的下载任务
   - 检查并下载播放列表中未缓存歌曲

4. 网络断开处理:
   - 切换到离线模式
   - 仅显示已缓存歌曲可播放
   - 暂停所有下载任务
   - Toast提示用户网络不可用

5. 飞行模式处理:
   - 立即暂停所有网络请求
   - 清空下载队列
   - 显示飞行模式提示
   - 仅允许播放本地缓存

时间复杂度: O(1) - 状态切换
响应时间: < 100ms
用户体验: 平滑过渡，最小化中断
```

### 9.8 OSS配置变更处理算法

**输入**: 新的OSS配置  
**输出**: 重新初始化连接  
**执行方式**: 配置验证 + 清理策略

```
算法: OSS配置变更处理
1. 配置变更检测:
   - 对比新旧配置的关键字段
   - 检测到bucket_name变更时触发清理流程
   - 其他配置变更仅重新连接

2. Bucket变更处理流程:
   - 暂停所有播放和下载
   - 弹窗确认: "更换存储桶将清空所有缓存，是否继续？"
   - 用户确认后执行清理

3. 缓存清理步骤:
   - 停止AudioPlayerManager
   - 清空播放队列
   - 删除Caches/AudioCache/下所有文件
   - 清空Core Data中的Song和CacheInfo记录
   - 重置所有歌单(保留结构，清空内容)

4. 重新初始化:
   - 使用新配置创建OSSClient
   - 测试连接可用性
   - 重新扫描OSS文件列表
   - 更新本地数据库
   - Toast提示"配置更新成功"

5. 错误处理:
   - 新配置无效: 保留旧配置，提示错误
   - 清理失败: 记录日志，部分清理
   - 连接失败: 降级到离线模式

时间复杂度: O(n) - n为缓存文件数
数据安全: 清理前确认，防止误操作
状态保持: 保存用户偏好设置
```

### 9.9 文件格式验证算法

**输入**: 歌曲文件路径  
**输出**: 是否支持播放  
**执行方式**: 扩展名检查 + AVAsset验证

```
算法: 文件格式验证
1. 快速验证(扩展名检查):
   支持格式 = ["mp3", "m4a", "flac", "wav"]
   if 文件扩展名.lowercased() in 支持格式:
       return 初步通过
   else:
       return 不支持

2. 深度验证(AVAsset检查):
   - 创建AVURLAsset
   - 检查isPlayable属性
   - 验证duration > 0
   - 检查音频轨道存在

3. 错误处理:
   - 不支持的格式: 跳过并Toast提示
   - 损坏的文件: 标记为不可用
   - 0字节文件: 直接过滤
   - 编码异常: 尝试降级处理

4. 性能优化:
   - 缓存验证结果
   - 批量验证使用并发队列
   - 优先验证即将播放的文件

时间复杂度: O(1) - 扩展名检查
深度验证: O(n) - 取决于文件大小
缓存策略: 验证结果持久化存储
```

### 9.10 MD5分块校验算法

**输入**: 文件URL, 预期MD5值  
**输出**: 校验是否通过  
**执行方式**: 分块读取 + 增量计算

```
算法: MD5分块校验
1. 分块策略:
   - 块大小: 1MB (1024 * 1024 bytes)
   - 使用FileHandle分块读取
   - 避免一次性加载大文件

2. 增量计算:
   let context = CC_MD5_CTX()
   CC_MD5_Init(&context)
   
   while let data = fileHandle.read(upToCount: blockSize) {
       data.withUnsafeBytes { bytes in
           CC_MD5_Update(&context, bytes, CC_LONG(data.count))
       }
       // 更新进度UI
   }
   
   CC_MD5_Final(digest, &context)

3. 异步执行:
   - 在downloadQueue中执行
   - 支持取消操作
   - 定期yield避免阻塞

4. 优化策略:
   - 小文件(<10MB)直接计算
   - 大文件显示进度条
   - 支持断点续算
   - 缓存已验证文件的MD5

时间复杂度: O(n) - n为文件大小
内存占用: O(1) - 固定缓冲区
性能影响: 异步执行，不阻塞主线程
```

### 9.11 搜索功能设计

#### 9.11.1 搜索算法设计
```
算法: 多字段模糊搜索
输入: searchQuery (搜索关键词)
输出: [Song] (匹配的歌曲列表)

1. 预处理搜索词:
   - 去除首尾空格
   - 转换为小写
   - 分词处理（按空格分割）

2. 构建复合查询谓词:
   对每个搜索词，在以下字段中进行模糊匹配：
   - title CONTAINS[cd] searchTerm
   - artist CONTAINS[cd] searchTerm  
   - album CONTAINS[cd] searchTerm
   - filePath CONTAINS[cd] searchTerm
   
   多个搜索词之间使用AND连接

3. 执行Core Data查询:
   - 使用NSFetchRequest
   - 设置谓词
   - 限制结果数量（首次100条）

4. 结果排序:
   - 标题完全匹配优先
   - 艺术家匹配次之
   - 其他字段匹配最后
   - 相同优先级按标题字母顺序

时间复杂度: O(n) - n为歌曲总数
优化策略: 建立全文搜索索引
防抖延迟: 300ms
```

#### 9.11.2 搜索性能优化
```
搜索优化策略
├── 输入优化
│   ├── 防抖处理（300ms延迟）
│   ├── 最小搜索长度（2个字符）
│   └── 取消前次搜索请求
├── 查询优化
│   ├── 使用索引字段
│   ├── 分页加载结果
│   └── 缓存搜索结果
└── 界面优化
    ├── 渐进式显示结果
    ├── 加载状态提示
    └── 空结果友好提示
```

#### 9.11.3 搜索交互流程
```mermaid
sequenceDiagram
    participant User as 用户
    participant SV as SearchView
    participant SVM as SearchViewModel
    participant SR as SongRepository
    participant CD as Core Data

    User->>SV: 输入搜索关键词
    SV->>SVM: 搜索请求（防抖300ms）
    SVM->>SVM: 取消前次搜索
    SVM->>SR: 执行搜索查询
    SR->>CD: 构建并执行NSFetchRequest
    CD-->>SR: 返回匹配结果
    SR-->>SVM: 搜索结果
    SVM-->>SV: 更新UI显示结果
    
    alt 无结果
        SV->>SV: 显示"没有找到相关歌曲"
    else 有结果
        SV->>SV: 显示歌曲列表
    end
```

## 10. UI/UX设计

### 10.1 歌单模块界面设计

#### 10.1.1 SongListsView（歌单列表主界面）
```
界面布局结构
├── 顶部搜索栏
│   ├── 搜索框（iOS原生样式）
│   ├── 点击展开全屏搜索
│   └── 支持实时搜索
├── 系统歌单区域
│   ├── 区域标题："系统歌单"
│   └── 歌单列表（固定5个）
│       ├── 我的收藏
│       ├── 最近播放
│       ├── 本地歌曲
│       ├── 全部
│       └── 未播放过
├── 自建歌单区域
│   ├── 区域标题："自建歌单(N)"
│   ├── 新建歌单按钮（+图标）
│   └── 歌单列表（可滚动）
└── 歌单项目布局
    ├── 左侧：歌单图标/封面
    ├── 中间：歌单名称 + 歌曲数量
    └── 右侧：播放按钮

交互设计：
- 点击歌单项：进入歌单详情
- 点击播放按钮：直接播放整个歌单
- 长按自建歌单：弹出菜单（重命名/删除）
- 下拉刷新：更新歌单信息
```

#### 10.1.2 SongListView（歌单详情界面）
```
界面布局结构
├── 顶部大图区域
│   ├── 第一首歌的专辑封面（模糊背景）
│   ├── 歌单名称（大标题）
│   └── 歌曲数量和总时长
├── 操作栏
│   ├── 全选复选框
│   ├── 播放按钮（播放全部）
│   ├── 添加到歌单（批量操作）
│   └── 更多操作（自建歌单）
└── 歌曲列表
    └── 歌曲项布局
        ├── 左侧：复选框
        ├── 中间上：歌曲名称
        ├── 中间下：艺术家 - 专辑
        ├── 右侧上：状态图标（❤️📱☁️）
        └── 右侧下：文件路径（小字）

交互设计：
- 点击歌曲（非复选框区域）：播放歌曲
- 勾选复选框：进入批量选择模式
- 长按歌曲：快速操作菜单
- 滑动删除：从歌单移除
- 拖拽排序：调整歌曲顺序
```

### 10.2 播放界面设计

#### 10.2.1 PlayerView（播放器主界面）
```
界面布局结构（有歌曲播放时）
├── 顶部栏
│   ├── 向下箭头（返回）
│   ├── "来自 [歌单/目录名]"
│   └── 网络状态图标
├── 专辑封面区域
│   ├── 大尺寸专辑图片
│   └── 无封面时显示音符图标
├── 歌曲信息区域
│   ├── 歌曲名称（大字体）
│   ├── 艺术家名称
│   └── 专辑名称
├── 进度控制区域
│   ├── 当前时间
│   ├── 进度条（可拖动）
│   └── 总时长
├── 播放控制区域
│   ├── 播放模式按钮（左）
│   ├── 上一首
│   ├── 播放/暂停（大按钮）
│   ├── 下一首
│   └── 音量控制（可选）
└── 底部操作栏
    ├── 收藏按钮
    ├── 添加到歌单
    └── 播放列表

空状态设计（无歌曲时）：
- 显示音符图标
- 提示文字："暂无播放歌曲"
- 副标题："从歌单或目录选择歌曲开始播放"
- 底部按钮保留但禁用
```

### 10.3 目录浏览界面设计

#### 10.3.1 FolderView（文件夹浏览界面）
```
界面布局结构
├── 路径导航栏
│   ├── 家图标（根目录）
│   ├── 面包屑路径（可点击）
│   └── 路径过长时省略中间部分
├── 操作栏
│   ├── 全选复选框
│   ├── 播放按钮
│   └── 添加到歌单按钮
└── 文件列表
    ├── 文件夹项
    │   ├── 文件夹图标
    │   ├── 文件夹名称
    │   └── 内部歌曲数量
    └── 文件项
        ├── 音乐图标
        ├── 文件名
        └── 文件大小

交互设计：
- 点击文件夹：进入子目录
- 点击文件：播放音乐
- 点击路径导航：快速跳转
- 批量选择：多选操作
- 播放文件夹：递归播放所有音乐
```

### 10.4 搜索界面设计

#### 10.4.1 SearchView（搜索界面）
```
界面布局结构
├── 搜索栏
│   ├── 搜索输入框
│   ├── 取消按钮
│   └── 清除按钮（输入时显示）
├── 搜索结果区域
│   ├── 结果数量提示
│   └── 歌曲列表（同歌单列表格式）
└── 空结果状态
    ├── 搜索图标
    └── "没有找到与'xxx'相关的结果"

交互设计：
- 实时搜索（300ms防抖）
- 支持批量操作
- 点击结果播放
- 历史搜索记录（可选）
```

### 10.5 设置界面设计

#### 10.5.1 SettingView（设置主界面）
```
界面布局结构
├── 阿里云配置组
│   ├── 配置状态（绿/红/灰）
│   ├── 进入配置按钮
│   └── 更新歌曲列表按钮
├── 播放设置组
│   ├── 边听边存开关
│   └── 音质选择（可选）
├── 缓存设置组
│   ├── 缓存上限选择器
│   ├── 当前缓存大小
│   └── 清理缓存按钮
├── 流量统计组
│   ├── 本月总流量
│   └── 移动网络流量
└── 其他设置组
    ├── 定时关闭
    ├── 关于应用
    └── 用户反馈

每个设置项布局：
- 左侧：设置名称和说明
- 右侧：控件/数值显示
```

### 10.6 弹出界面设计

#### 10.6.1 底部弹出视图通用设计
```
通用弹出视图结构
├── 拖动指示条
├── 标题栏
│   ├── 取消/返回按钮
│   ├── 标题文字
│   └── 确认/完成按钮
├── 内容区域
│   └── 具体内容
└── 安全区域适配

交互设计：
- 下滑关闭
- 点击背景关闭
- 键盘弹出时自动上移
```

### 10.7 动画和过渡设计
```
动画效果设计
├── 页面转场
│   ├── push/pop: 标准滑动
│   ├── present: 底部弹出
│   └── dismiss: 向下收起
├── 列表动画
│   ├── 插入/删除: 淡入淡出
│   ├── 拖拽排序: 平滑移动
│   └── 刷新: 弹性效果
├── 播放状态
│   ├── 播放按钮: 形态过渡
│   ├── 进度条: 平滑更新
│   └── 封面切换: 淡入淡出
└── 加载状态
    ├── 骨架屏（列表）
    ├── 进度环（下载）
    └── Toast提示（轻量）
```

### 10.8 适配设计
```
设备适配策略
├── 屏幕尺寸
│   ├── iPhone 11: 基准设计
│   ├── 小屏设备: 内容优先
│   └── 大屏设备: 合理留白
├── 安全区域
│   ├── 顶部刘海适配
│   └── 底部指示条适配
└── 横竖屏
    └── 仅支持竖屏
```

## 11. 错误处理设计

### 11.1 错误分类层次

```
AppError体系设计
├── NetworkError (网络相关)
│   ├── NetworkUnavailable (网络不可用)
│   ├── ConnectionTimeout (连接超时)
│   ├── InvalidResponse (响应无效)
│   └── RequestRateLimited (请求限流)
├── OSSError (云存储相关)
│   ├── AuthenticationFailed (认证失败)
│   ├── BucketNotFound (存储桶不存在)
│   ├── FileNotFound (文件不存在)
│   ├── UploadFailed (上传失败)
│   └── DownloadFailed (下载失败)
├── AudioError (音频相关)
│   ├── PlaybackFailed (播放失败)
│   ├── UnsupportedFormat (格式不支持)
│   ├── CorruptedFile (文件损坏)
│   └── HardwareUnavailable (硬件不可用)
├── StorageError (存储相关)
│   ├── InsufficientSpace (空间不足)
│   ├── PermissionDenied (权限拒绝)
│   ├── DatabaseCorrupted (数据库损坏)
│   └── FileSystemError (文件系统错误)
└── SyncError (同步相关)
    ├── ConflictDetected (冲突检测)
    ├── SyncTimeout (同步超时)
    ├── DataInconsistent (数据不一致)
    └── VersionMismatch (版本不匹配)
```

### 11.2 Toast显示策略

#### 11.2.1 优先级队列系统
```
Toast优先级系统
├── Critical (3) ────▶ 立即显示，5秒持续，阻塞UI
├── High (2)     ────▶ 优先队列，4秒持续，可操作UI
├── Normal (1)   ────▶ 正常队列，3秒持续，可操作UI
└── Low (0)      ────▶ 延后显示，2秒持续，可操作UI

队列管理:
- 最大队列长度: 5个
- 超出时丢弃最低优先级
- 相同内容合并显示
- 批量错误合并提示
```

#### 11.2.2 错误恢复策略
| 错误类型 | 恢复策略 | 用户提示 | 自动重试 | 降级方案 |
|----------|----------|----------|----------|----------|
| 网络超时 | 后台重试 | Toast提示 | 3次，指数退避 | 离线模式 |
| 文件损坏 | 跳过播放 | Toast + 下一首 | 0次 | 移除队列 |
| 存储不足 | 清理缓存 | Toast + 清理结果 | 1次 | 禁用缓存 |
| 权限拒绝 | 引导设置 | 弹窗引导 | 0次 | 功能降级 |
| 播放失败 | 下一首 | Toast提示 | 2次 | 暂停播放 |

## 12. 性能优化策略

### 12.1 启动性能优化

#### 12.1.1 启动时间目标
| 启动阶段 | 目标时间 | 优化策略 | 监控指标 |
|----------|----------|----------|----------|
| 应用启动 | < 1秒 | 延迟初始化、预编译 | Time to First Frame |
| 数据加载 | < 2秒 | 增量加载、缓存复用 | Time to Interactive |
| 界面渲染 | < 0.5秒 | 预布局、懒加载 | First Contentful Paint |
| 总启动时间 | < 3秒 | 并行初始化、优先级 | Launch Duration |

#### 12.1.2 启动优化策略
```
启动性能优化
├── 减少启动工作量
│   ├── 延迟非关键模块初始化
│   ├── 预编译频繁访问的查询
│   ├── 缓存重复计算结果
│   └── 并行化独立任务
├── 优化资源加载
│   ├── 压缩应用包大小
│   ├── 优化图片资源格式
│   ├── 减少动态库依赖
│   └── 预加载关键资源
└── 改善用户感知
    ├── 添加启动画面
    ├── 渐进式界面显示
    ├── 骨架屏占位
    └── 预渲染关键界面
```

### 12.2 列表性能优化

#### 12.2.1 虚拟滚动实现
```
列表性能优化策略
├── 视窗管理
│   ├── 计算可见区域
│   ├── 预加载缓冲区 (上下各10项)
│   ├── 回收不可见Cell
│   └── 动态调整缓冲区大小
├── 数据分页
│   ├── 初始加载: 50项
│   ├── 滚动加载: 每次20项
│   ├── 预加载阈值: 剩余10项
│   └── 最大内存项数: 500项
├── 渲染优化
│   ├── Cell复用机制
│   ├── 异步图片加载
│   ├── 延迟计算布局
│   └── 批量UI更新
└── 内存管理
    ├── 及时释放图片内存
    ├── 压缩非关键数据
    ├── 监控内存使用
    └── 内存警告处理
```

### 12.3 网络性能优化

#### 12.3.1 网络请求优化
| 优化类型 | 策略 | 实现方式 | 性能指标 |
|----------|------|----------|----------|
| 连接复用 | HTTP/2 Keep-Alive | URLSession配置 | 连接建立次数 |
| 请求合并 | 批量操作API | 业务层合并 | 请求数量 |
| 缓存策略 | 多级缓存 | NSURLCache + 业务缓存 | 缓存命中率 |
| 压缩传输 | gzip/br压缩 | 服务端配置 | 传输大小 |
| 断点续传 | Range请求 | URLSessionDownloadTask | 下载效率 |

### 12.4 内存性能优化

#### 12.4.1 内存使用监控
```
内存管理策略
├── 内存监控
│   ├── 实时内存使用跟踪
│   ├── 内存泄漏检测
│   ├── 内存警告响应
│   └── 峰值内存记录
├── 缓存管理
│   ├── 图片缓存: 50MB限制
│   ├── 数据缓存: 动态调整
│   ├── 网络缓存: 系统管理
│   └── 临时缓存: 及时清理
├── 对象生命周期
│   ├── 弱引用避免循环
│   ├── 及时释放大对象
│   ├── 延迟创建重对象
│   └── 复用常用对象
└── 内存压力处理
    ├── 清理非关键缓存
    ├── 暂停后台任务
    ├── 释放预加载资源
    └── 通知用户内存不足
```

### 12.5 性能基准

#### 12.5.1 关键性能指标
| 场景 | 性能指标 | 目标值 | 测试条件 |
|------|----------|--------|----------|
| 1000首歌曲加载 | 列表渲染时间 | < 1秒 | iPhone 11 |
| 搜索响应 | 首个结果返回 | < 300ms | 本地搜索 |
| 内存占用 | 正常使用 | < 100MB | 播放中状态 |
| CPU占用 | 音频播放 | < 5% | 后台播放 |
| 缓存清理 | 100首歌曲 | < 2秒 | LRU算法 |
| 文件夹扫描 | 500个文件 | < 3秒 | 3层目录 |
| 页面切换 | 响应时间 | < 100ms | 任意页面 |
| 电池消耗 | 后台播放1小时 | < 3% | 屏幕关闭 |

### 12.6 功耗优化策略

#### 12.6.1 低功耗设计原则
```
功耗优化架构
├── CPU优化
│   ├── 避免空转轮询
│   ├── 使用系统定时器替代自定义循环
│   ├── 后台任务批量执行
│   └── 及时释放不必要的计算任务
├── 网络优化
│   ├── 批量请求减少唤醒次数
│   ├── 使用URLSession后台配置
│   ├── 避免频繁的网络状态检查
│   └── 合理设置超时时间
├── 存储优化
│   ├── 批量写入减少IO操作
│   ├── 使用异步IO避免阻塞
│   ├── 合理的缓存策略减少重复读取
│   └── 避免频繁的小文件操作
└── 界面优化
    ├── 减少不必要的动画
    ├── 后台时停止UI更新
    ├── 使用系统优化的UI组件
    └── 避免过度绘制
```

#### 12.6.2 后台播放功耗控制
| 优化项 | 策略 | 预期效果 |
|--------|------|----------|
| 音频会话 | 使用AVAudioSession优化配置 | 降低20%功耗 |
| 网络请求 | 后台时降低同步频率 | 减少网络唤醒 |
| 数据更新 | 批量更新播放统计 | 减少数据库IO |
| 预加载 | 后台时暂停预加载 | 避免不必要下载 |
| 日志记录 | 后台时关闭详细日志 | 减少文件写入 |

## 13. 部署与发布

### 13.1 构建配置

#### 13.1.1 环境配置矩阵
| 配置项 | Debug | Release | 说明 |
|--------|-------|---------|------|
| 编译优化 | -Onone | -O | 性能vs调试 |
| 调试符号 | 包含完整 | 剥离上传 | 调试vs包大小 |
| 代码签名 | 开发证书 | 分发证书 | 环境隔离 |
| 网络配置 | 测试环境 | 生产环境 | 数据隔离 |
| 日志级别 | Debug | Error | 信息安全 |
| 崩溃上报 | 开发版本 | 生产版本 | 问题追踪 |

#### 13.1.2 依赖管理
```
项目依赖配置
├── 核心依赖
│   ├── AliyunOSSiOS (~> 2.10.18)
│   ├── SwiftUI (系统内置)
│   └── Combine (系统内置)
├── 开发依赖
│   ├── SwiftLint (代码检查)
│   ├── XCTest (单元测试)
│   └── SwiftyMocky (Mock测试)
└── 可选依赖
    ├── SwiftUIX (UI扩展,可选)
    ├── CombineExt (Combine扩展,可选)
    └── KeychainAccess (Keychain封装,可选)
```

### 13.2 发布清单

#### 13.2.1 预发布检查
- [ ] **功能完整性**: 所有需求功能实现并测试通过
- [ ] **性能指标**: 启动时间 < 3秒, 内存占用 < 100MB
- [ ] **兼容性**: iPhone 11及以上 + iOS 15.5+测试通过
- [ ] **网络测试**: 弱网络环境、网络切换测试
- [ ] **权限配置**: 后台音频、网络访问权限正确配置
- [ ] **数据安全**: 敏感数据加密、权限控制验证
- [ ] **错误处理**: 异常场景处理、用户友好提示
- [ ] **界面适配**: iPhone 11/12/13/14系列适配验证

#### 13.2.2 App Store配置
```
App Store发布配置
├── 应用信息
│   ├── 名称: "阿拉摩音乐"
│   ├── 副标题: "私人云音乐播放器"
│   ├── 关键词: "音乐播放器,OSS播放器,私人音乐库"
│   └── 描述: "用于播放您存储在阿里云OSS上的个人音乐收藏"
├── 技术信息
│   ├── 支持设备: iPhone
│   ├── 最低版本: iOS 15.5
│   ├── 后台模式: 音频播放
│   └── 网络使用: HTTPS required
└── 隐私政策
    ├── 数据收集说明
    ├── 第三方服务使用
    ├── 用户控制权说明
    └── 联系方式
```

## 14. 安全设计

### 14.1 数据安全策略

#### 14.1.1 数据保护等级矩阵
| 数据类型 | 敏感等级 | 存储方式 | 加密算法 | 访问控制 |
|----------|----------|----------|----------|----------|
| OSS Access Key | 极高 | Keychain | AES-256-GCM | 应用独占 |
| 用户密码 | 高 | Keychain | AES-256-GCM | 生物识别 |
| 播放历史 | 中 | Core Data | 文件系统加密 | 应用沙盒 |
| 歌曲元数据 | 低 | Core Data | 文件系统加密 | 应用沙盒 |
| 临时文件 | 低 | Caches | 无 | 应用沙盒 |

#### 14.1.2 网络安全配置
```
网络安全策略
├── TLS配置
│   ├── 最低TLS 1.2版本
│   ├── 强制证书验证
│   ├── 证书固定 (可选)
│   └── HSTS支持
├── 请求安全
│   ├── 请求签名验证
│   ├── 时间戳防重放
│   ├── 敏感数据加密
│   └── 请求频率限制
└── 数据传输
    ├── 关键数据HTTPS传输
    ├── 文件传输完整性校验
    ├── 传输过程加密
    └── 断点续传安全
```

### 14.2 隐私保护设计

#### 14.2.1 数据收集原则
```
隐私保护策略
├── 数据最小化
│   ├── 仅收集必要数据
│   ├── 本地优先处理
│   ├── 定期清理冗余
│   └── 用户可控制
├── 透明度原则
│   ├── 明确数据用途
│   ├── 清晰权限说明
│   ├── 数据处理通知
│   └── 选择退出机制
└── 安全存储
    ├── 本地加密存储
    ├── 访问权限控制
    ├── 数据传输加密
    └── 定期安全检查
```

---

*本设计文档专注于架构设计、流程图、模块定义和交互方式，为开发团队提供清晰的技术架构指导，支持高质量的iOS应用开发实施。* 