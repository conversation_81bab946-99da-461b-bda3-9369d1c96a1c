我需要实现一个ios应用，由swiftui开发，xcode版本14.3.1，目标iPhone 11，支持ios 15.5。
下面是我的需求，请根据我的需求找出不合适的地方+没有明确的地方+容易产生歧义的地方+更优化的地方+细节描述不清晰的地方，以确保在后续AI生成代码时不会有遗漏或与我想法不会有偏差。
注意：在给出问题时，应给出选择，选择包含你推荐的一种或几种+其他选择
底部标签栏
    歌单 - SongListView
    播放
        点击后，弹出全屏播放页面
        有歌曲正在播放时，显示 正在播放
        无歌曲正在播放时，显示 播放
    目录 - FolderView
    设置 - SettingView
歌单 - SongListsView
    区域1：系统歌单
        展示歌单的列表
        包含的系统歌单：
            我的收藏
            最近播放
            本地歌曲
            全部
            未播放过（从没播放过的歌曲）
        每个歌单：
            展示：歌单图片（显示的是系统默认图标）
                歌单名称、包含多少首歌
                是否播放的按钮
            点击动作：
                点击播放按钮：
                    播放这个歌单，打开播放view
                点击其他位置：
                    可以打开歌单View
    区域2：自建歌单（展示歌单数量）
        展示每个自建歌单
        歌单展示方式与系统歌单类似，只是图标有区别
            图标展示：歌单内第一首歌的专辑图片

    ---
    歌单View - SongList.view
        区域1：第一首歌的专辑图片
        区域2：
            左边：多选框，可以全选/取消全选
            右边：按钮（播放）+文字（播放）
                点击后，可以从第一首歌开始，播放这个歌单
            最右：添加到歌单
                未选择歌曲时，灰色
                点击后，弹出添加到歌单view
        区域3：歌曲列表
            歌曲有复选框
                点击复选框后，选中/取消选中
            每首歌内容：
                歌曲名称
                歌手
                歌曲是否收藏
                歌曲是否已下载到本地
                歌曲位置（/xxx.mp3，或者 /xx作者/xx.mp3）
            点击任一首歌后（不是复选框区域）
                动作：
                    点击后有类似按钮的点击动画
                    点击后将从该歌曲开始播放该歌单
            歌曲列表可以下滑
                下滑时有右侧下滑条，可以手动选择这个条目
播放 - PlayerView
    返回按钮：改为下箭头
    触摸滑动返回：
        在屏幕任何位置，向下滑动，可以返回
        在屏幕左侧滑动也可返回（原来的返回手势方式也要支持）
    打开后动作：
        全屏展示播放view，不需要在最下面展示歌单、设置等tab标签
    展示内容：
        区域1：顶层（从左到右介绍）
            最左侧：返回按钮（向下的箭头，类似于最小化）
            中间：来自 歌单/目录
                （如果是目录，则为相对目录，不展示系统自动创建的那一层）
        区域2：上面
            音乐专辑封面
                如果没有的话，就用Image(systemName: "music.note")
        区域3：专辑封面下面 音乐介绍（分多行展示）
            音乐名
            歌唱者
            专辑名称
        区域4：播放进度条
        区域5：播放控制区域
            播放模式（最左侧，图标小一点）、
            上一首歌、播放/暂停、下一首歌、

        区域6：歌曲设置
            收藏（上面心形，下面为文字，当已经收藏的歌曲时展示为红心，当未收藏歌曲时展示为空的心形）
            添加到歌单
            播放列表

    ---
    添加歌单view
        占用下面的3/4屏幕
        选择自建的歌单
            单选，选择歌单名字后，即添加完成并返回上一个view
            点击空白位置，则相当于返回
            也有取消按钮，可以取消添加歌单的操作
            可以新建歌单，点击新建歌单后，弹出新建歌单view

    新建歌单view
        占用下面的1/2屏幕
        顶部为取消、创建按钮
        下面为新建歌单的名称，
            默认为“新建歌单1”，并且默认选中，且弹出键盘输入框
                以支持按一个删除按键就能重新输入歌单名称
目录View - FolderView
    区域1：
        最左：一个家的标识
            点击后，回到最上层/（这里对应的是<bucket>/alamusic/路径）
        紧挨着展示：目录路径（每个目录一个小模块，）
            要求：字比较小
            每个中间目录可以直接点击，点击后跳转到这层目录
            如果路径太长，优先显示后面部分
    区域2：选择与操作
        多选框（可一键全选/取消全选）
        右侧有播放按钮，未选择时播放按钮为播放全部
            点击后，可以播放这个目录下所有音乐（包含子目录）
        再右侧有添加到歌单按钮
            点击后，可以将选中的歌曲添加到歌单
                （未选中时不可点击）
                弹出添加歌单view
    区域2：
        文件/文件夹内容展示
        如果是文件：
            点击后，播放这个文件，并弹出播放view
                播放view返回后，回到这个界面
            显示文件大小
        如果是文件夹
            显示内部歌曲个数
设置View - SettingView
    区域1：阿里云对象存储配置
        配置阿里云对象存储
            点击跳转 - 阿里云配置View
        oss连接状态展示
            如成功：绿色，oss连接成功
            如失败：
                左：红色，oss连接失败
                右：重新连接
            如未配置：灰色，oss未配置
        更新歌曲列表
            点击后：
                弹出提示：将清空本地缓存，并且会停止播放
                    （先简单以覆盖方式做，后面支持更新的方式）
                    （并且后续要支持远程文件播放信息管理，比如压缩或转换为mp3格式）
                从oss更新歌曲列表
            显示：
                未更新时：
                    更新歌曲列表
                更新时：
                    更新歌曲列表（灰色，禁止点击）
                    小字：正在更新歌曲列表

    区域2：播放与下载
        边听边存（可配置）
            边听边存开关，默认关闭
        音乐缓存上限（可配置）
            跳转：音乐缓存上限设置View
            ---
            音乐缓存上限设置View
                标题：音乐缓存上限
                单选（竖排）：
                    不缓存、100M、500M、800M、1G、2G、4G、8G
                返回时保存
        本月流量统计： 2GB
            展示本月下载使用了多少流量
        移动流量统计： 1GB
            展示本月通过移动网络下载使用了多少流量

    ---
    阿里云配置View - AliyunSettingView
        内容：
            Endpoint
                Endpoint文字+输入框
            Access Key ID
                文字+输入框
            Access Key Secret
                文字+密文输入框
            Bucket Name
                文字+输入框
            备注：
                文字+输入框（多行）
        返回时：自动保存配置
            比对用于连接的配置变更
                如果变更，自动重连
                如果没变，保持不动

        启动时：
            先加载：从本地存储中加载信息，显示
            再连接oss：
                如果没有配置，则跳过连接
                连接到阿里云存储
                下载云存储的歌曲信息
                    如果超大，则跳过
                使用云存储的歌曲信息，覆盖本地信息
                    本地
全局效果说明：
    所有播放歌曲时，都会跳转到播放列表
        播放列表返回时，都会回到之前播放的位置
阿里云存储结构：
    <bucket>/alamusic/
        .alamusic.data/ --这个目录下存放alamusic的设置
        xxdir -- 这里可以是xx目录，支持多级子目录
        xxfile.mp3 --这里可以是xx音乐文件
内存存储说明：
    只有一个地方存储音乐列表，其他地方都存储这个列表的id
关于后台作业：
    启动时：
        阻塞从本地存储加载数据
        异步尝试连接到oss
    播放音乐：
        每首歌优先从本地缓存获取内容后直接播放
        如果本地没有
            每首歌有5秒钟尝试连接远程，如果连接不上则跳过这首歌，进行下一首歌
            下载完成后，再进行播放，暂不支持边下边播

