# 阿拉摩音乐 iOS 应用需求文档

## 1. 项目概述

### 1.1 项目背景
开发一个iOS音乐播放应用，用于播放用户存储在阿里云OSS上的个人音乐文件。

### 1.2 技术要求
- 开发框架：SwiftUI 3.0（iOS 15兼容功能集）
- Xcode版本：14.3.1
- 目标设备：iPhone 11
- 最低系统版本：iOS 15.5
- 音频框架：AVPlayer + MediaPlayer
- 本地存储：Core Data
- 预留Dynamic Island适配空间（iOS 16.1+）*Dynamic Island仅支持iPhone 14 Pro及以上机型

### 1.3 核心功能
- 云端音乐文件管理和播放
- 本地缓存和离线播放
- 歌单管理
- 文件目录浏览
- 搜索功能
- 后台播放支持

## 2. 界面结构

### 2.1 底部标签栏（TabBar）
包含4个标签：
1. **歌单** - SongListsView
2. **播放** - 动态显示
   - 有歌曲播放时：显示"正在播放"
   - 无歌曲播放时：显示"播放"
   - 点击行为：
     - 有歌曲播放：打开播放界面
     - 无歌曲播放：播放"最近播放"第一首，若为空则播放"全部"歌单
3. **目录** - FolderView
4. **设置** - SettingView

## 3. 功能模块详细说明

### 3.1 歌单模块（SongListsView）

#### 3.1.1 界面布局
- **顶部搜索栏**：iOS原生样式，点击展开搜索View
- **区域1：系统歌单**
  - 固定歌单列表：
    - 我的收藏
    - 最近播放（最多200首，按最后播放时间排序）
    - 本地歌曲
    - 全部（包括本地+云端索引）
    - 未播放过
  - 每个歌单显示：
    - 系统默认图标
    - 歌单名称
    - 歌曲数量
    - 播放按钮
  
- **区域2：自建歌单**
  - 显示歌单数量
  - 图标：歌单内第一首歌的专辑封面（无封面时显示默认图标）
  - 支持创建、删除、重命名

#### 3.1.2 歌单详情页（SongListView）
- **顶部**：第一首歌的专辑图片
- **操作栏**：
  - 全选/取消全选复选框
  - 播放按钮（播放整个歌单）
  - 添加到歌单按钮（未选中时禁用）
  - 右上角三点菜单（自建歌单）：重命名、删除
  
- **歌曲列表**：
  - 始终显示复选框
  - 显示内容：
    - 歌曲名称
    - 歌手
    - 收藏状态（❤️）
    - 下载状态（📱）
    - 云端状态（☁️）
    - 文件路径（相对路径，小字显示）
  - 支持功能：
    - 点击播放（非复选框区域）
    - 批量选择
    - 批量添加到歌单
    - 批量删除（从歌单移除/删除缓存）（此操作仅移出歌单，不影响文件存储）
    - 滑动删除单个项目
    - 拖拽排序（实时保存）
    - 歌曲排序：按加入时间，最早加入的在前

### 3.2 搜索功能

#### 3.2.1 搜索View
- 从歌单页面顶部搜索框进入
- 全屏显示，包含：
  - 搜索输入框
  - 取消按钮
  - 搜索结果列表
- 搜索范围：歌曲名称、艺术家、专辑、文件路径
- 实时搜索（300ms防抖）
- 支持批量操作（独立选中状态）
- 空结果提示："没有找到与'xxx'相关的结果"
- 搜索结果格式与歌单列表相同
- 支持的操作：
  - 播放
  - 添加到歌单
  - 收藏/取消收藏
  - 查看详情

### 3.3 播放界面（PlayerView）

#### 3.3.1 界面布局
- **顶部栏**：
  - 返回按钮（向下箭头）
  - 来源显示："来自 歌单/目录"
  - 网络状态图标（实时更新，无点击交互）
  
- **主要内容**：
  - 专辑封面（无封面显示music.note图标）
  - 歌曲信息（多行）：
    - 歌曲名
    - 歌手
    - 专辑名称
  - 播放进度条
  
- **播放控制**：
  - 播放模式（左侧，小图标）：顺序、随机、单曲循环、列表循环
  - 上一首
  - 播放/暂停
  - 下一首
  
- **底部操作**：
  - 收藏（红心/空心）
  - 添加到歌单
  - 播放列表

- **无歌曲时的播放界面**：
  - 显示提示语："暂无播放歌曲"
  - 不显示专辑封面/进度条等播放控件
  - 保留顶部返回按钮和底部操作栏（操作按钮禁用状态）

#### 3.3.2 手势支持
- 下滑返回（主要手势）
- 左侧边缘右滑返回（备选）

### 3.4 播放列表View
- 从底部弹出，占屏幕3/4高度
- 显示当前播放队列
- 当前播放歌曲高亮
- 支持：
  - 点击切换歌曲
  - 拖拽排序（随机模式下仍可拖拽，只影响原始顺序）
  - 删除队列中的歌曲

### 3.5 添加到歌单View
- 从底部弹出，占屏幕3/4高度
- 显示自建歌单列表
- 单选模式，选中即添加并返回
- 包含新建歌单按钮
- 点击空白或取消按钮返回

### 3.6 新建歌单View
- 从底部弹出，占屏幕1/2高度
- 顶部：取消、创建按钮
- 输入框：默认"新建歌单1"，自动选中并弹出键盘

### 3.7 目录模块（FolderView）

#### 3.7.1 界面布局
- **路径导航栏**：
  - 家图标（回到/alamusic/根目录）
  - 面包屑导航（可点击跳转）
  - 路径过长时优先显示后部
  
- **操作栏**：
  - 全选/取消全选
  - 播放按钮（播放目录下所有音乐，包含子目录）
  - 添加到歌单（未选中时禁用）
  
- **文件列表**：
  - 文件：显示大小，点击播放
  - 文件夹：显示内部歌曲数量，点击进入
  - 支持批量操作

### 3.8 设置模块（SettingView）

#### 3.8.1 阿里云配置
- 配置入口
- 连接状态显示：
  - 绿色：连接成功
  - 红色：连接失败（含重连按钮）
  - 灰色：未配置
- 更新歌曲列表按钮（更新时禁用并显示进度）
- 状态检查：进入页面时+每30秒

#### 3.8.2 播放与下载设置
- 边听边存开关（默认关闭）
- 音乐缓存上限：不缓存、100M、500M、800M、1G、2G、4G、8G
- 本月流量统计（自然月）
- 移动流量统计
- 定时关闭：15/30/60分钟（暂停播放并保存状态）

### 3.9 阿里云配置View（AliyunSettingView）
- 配置项：
  - Endpoint
  - Access Key ID
  - Access Key Secret（密文）
  - Bucket Name
  - 备注（多行）
- 返回时自动保存
- 配置变更自动重连
- 实时验证 + 保存时验证
- 使用加密存储Access Key

## 4. 数据模型设计

### 4.1 Core Data实体
- **Song**（歌曲）
- **SongList**（歌单）
- **PlayHistory**（播放历史）
- **AppSettings**（应用设置）
- **CacheInfo**（缓存信息）

### 4.2 播放统计
记录内容：
- 播放次数
- 最后播放时间
- 累计播放时长
- 歌曲开始播放即标记为已播放（无时长阈值要求），更新以下字段：
  - 播放次数 +1
  - 最后播放时间 = 当前时间
  - 累计播放时长 从0秒开始累计

## 5. 播放和缓存策略

### 5.1 播放策略
- 优先播放本地缓存
- 无缓存时从OSS下载（5秒超时）
- 下载失败跳过到下一首
- 支持格式：MP3、M4A、FLAC、WAV
- 后台播放跳过不支持的格式并提示

### 5.2 缓存策略
- 使用Caches目录，分层目录结构：
  ```
  Caches/
    alamusic_cache/
      bucket_name/
        [原始文件路径结构]
  ```
- LRU清理算法（按歌曲的最后播放时间戳清理）
- 下载中的文件受保护
- 当前播放列表尽量保护
- 缓存满时删除距离当前最远的歌曲

### 5.3 预加载策略
- WiFi网络：预加载后续 2-3首 歌曲
- 移动网络：预加载后续 1首 歌曲
- 不提供预加载开关（始终启用）
- 切换播放列表时取消当前预加载
- 播放时检查网络状态

### 5.4 下载策略
- 单文件并发下载
- WiFi切换移动网络：完成当前下载后重新规划
- 飞行模式：暂停下载
- 文件完整性：MD5校验

## 6. 网络和同步策略

### 6.1 OSS连接
- 启动时异步连接
- 连接失败后台重试30秒
- 手动更新或下载前检测文件
- 下载失败标记为"不可用"

### 6.2 数据同步
- 实时同步（失败5分钟重试）：
  - 自建歌单信息
  - 收藏歌曲列表
  - 应用设置信息
- 延迟同步（5分钟）：
  - 播放历史
  - 播放统计
- 多设备冲突：后写入覆盖
- 同步位置：.alamusic.data/目录

### 6.3 播放列表管理
- 播放时替换当前播放列表
- 来源记忆：歌单/目录/搜索结果
- 直接切换无需确认

## 7. 系统集成

### 7.1 后台播放
- 支持后台音频播放
- 锁屏控制
- 控制中心显示
- 耳机线控支持
- 需配置AVAudioSession.Category.playback权限

### 7.2 状态恢复
- 应用被杀死后恢复：
  - 播放列表
  - 当前歌曲（暂停在0:00）
  - 播放模式
- 及时保存关键信息

## 8. 错误处理

### 8.1 Toast提示
- 使用Toast队列按优先级显示
- 错误类型：
  - OSS连接失败
  - 文件下载失败
  - 音频播放失败
  - 磁盘空间不足

### 8.2 异常处理
- 空间不足：停止下载+LRU清理
- 异常文件：跳过（0字节、损坏、不支持的编码）
- 并发冲突：
  - 播放中删除→下一首
  - 下载中移出歌单→继续下载
  - 上传中切换账号→停止上传

## 9. 性能优化

### 9.1 列表性能
- LazyVStack/List虚拟滚动
- 图片异步加载
- 搜索防抖300ms

### 9.2 数据加载
- Core Data批量插入
- 目录异步加载+增量更新
- OSS分页加载（100-200个/页）

## 10. 视觉设计

### 10.1 图标系统
- 系统歌单：默认图标
- 自建歌单：首歌封面/默认图标
- 文件状态：
  - 可播放云端：正常+☁️
  - 不可用云端：灰色+☁️
  - 本地缓存：正常+📱

### 10.2 交互反馈
- 点击动画效果
- 加载骨架屏（如实现困难可用loading）
- 下拉刷新

## 11. 其他约定

### 11.1 启动流程
1. 阻塞加载Core Data
2. 显示界面
3. 异步连接OSS
4. 同步云端数据

### 11.2 存储结构
- OSS: `<bucket>/alamusic/`
  - `.alamusic.data/` - 应用数据
  - 音乐文件和目录

### 11.3 App Store
- 说明"播放用户自有音乐文件"

### 11.4 未来扩展
- 歌词支持（预留能力）
- iPad适配（暂不实现）